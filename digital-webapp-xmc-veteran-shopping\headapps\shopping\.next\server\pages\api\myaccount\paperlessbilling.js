"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/myaccount/paperlessbilling";
exports.ids = ["pages/api/myaccount/paperlessbilling"];
exports.modules = {

/***/ "@microsoft/applicationinsights-react-js":
/*!**********************************************************!*\
  !*** external "@microsoft/applicationinsights-react-js" ***!
  \**********************************************************/
/***/ ((module) => {

module.exports = require("@microsoft/applicationinsights-react-js");

/***/ }),

/***/ "@microsoft/applicationinsights-web":
/*!*****************************************************!*\
  !*** external "@microsoft/applicationinsights-web" ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = require("@microsoft/applicationinsights-web");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "axios-1.4":
/*!****************************!*\
  !*** external "axios-1.4" ***!
  \****************************/
/***/ ((module) => {

module.exports = import("axios-1.4");;

/***/ }),

/***/ "hashids":
/*!**************************!*\
  !*** external "hashids" ***!
  \**************************/
/***/ ((module) => {

module.exports = import("hashids");;

/***/ }),

/***/ "iron-session/next":
/*!************************************!*\
  !*** external "iron-session/next" ***!
  \************************************/
/***/ ((module) => {

module.exports = import("iron-session/next");;

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmyaccount%2Fpaperlessbilling&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cmyaccount%5Cpaperlessbilling%5Cindex.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmyaccount%2Fpaperlessbilling&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cmyaccount%5Cpaperlessbilling%5Cindex.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_myaccount_paperlessbilling_index_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\myaccount\\paperlessbilling\\index.ts */ \"(api)/./src/pages/api/myaccount/paperlessbilling/index.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_api_myaccount_paperlessbilling_index_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_src_pages_api_myaccount_paperlessbilling_index_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_myaccount_paperlessbilling_index_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_myaccount_paperlessbilling_index_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/myaccount/paperlessbilling\",\n        pathname: \"/api/myaccount/paperlessbilling\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_myaccount_paperlessbilling_index_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmyaccount%2Fpaperlessbilling&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cmyaccount%5Cpaperlessbilling%5Cindex.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/app-insights.ts":
/*!*********************************!*\
  !*** ./src/lib/app-insights.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appInsights: () => (/* binding */ appInsights),\n/* harmony export */   reactPlugin: () => (/* binding */ reactPlugin)\n/* harmony export */ });\n/* harmony import */ var _microsoft_applicationinsights_web__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @microsoft/applicationinsights-web */ \"@microsoft/applicationinsights-web\");\n/* harmony import */ var _microsoft_applicationinsights_web__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_microsoft_applicationinsights_web__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _microsoft_applicationinsights_react_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @microsoft/applicationinsights-react-js */ \"@microsoft/applicationinsights-react-js\");\n/* harmony import */ var _microsoft_applicationinsights_react_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_microsoft_applicationinsights_react_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst reactPlugin = new _microsoft_applicationinsights_react_js__WEBPACK_IMPORTED_MODULE_1__.ReactPlugin();\nconst appInsights = new _microsoft_applicationinsights_web__WEBPACK_IMPORTED_MODULE_0__.ApplicationInsights({\n    config: {\n        connectionString: \"InstrumentationKey=182a1956-82c3-4456-b26b-167a83e1066a;IngestionEndpoint=https://southcentralus-0.in.applicationinsights.azure.com/;LiveEndpoint=https://southcentralus.livediagnostics.monitor.azure.com/\",\n        enableAutoRouteTracking: true,\n        extensions: [\n            reactPlugin\n        ]\n    }\n});\nappInsights.loadAppInsights();\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvbGliL2FwcC1pbnNpZ2h0cy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBeUU7QUFDSDtBQUV0RSxNQUFNRSxjQUFjLElBQUlELGdGQUFXQTtBQUNuQyxNQUFNRSxjQUFjLElBQUlILG1GQUFtQkEsQ0FBQztJQUMxQ0ksUUFBUTtRQUNOQyxrQkFBa0JDLDZNQUFxRDtRQUN2RUcseUJBQXlCO1FBQ3pCQyxZQUFZO1lBQUNSO1NBQVk7SUFDM0I7QUFDRjtBQUVBQyxZQUFZUSxlQUFlO0FBRVMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zaG9wcGluZy8uL3NyYy9saWIvYXBwLWluc2lnaHRzLnRzPzU3NDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwbGljYXRpb25JbnNpZ2h0cyB9IGZyb20gJ0BtaWNyb3NvZnQvYXBwbGljYXRpb25pbnNpZ2h0cy13ZWInO1xyXG5pbXBvcnQgeyBSZWFjdFBsdWdpbiB9IGZyb20gJ0BtaWNyb3NvZnQvYXBwbGljYXRpb25pbnNpZ2h0cy1yZWFjdC1qcyc7XHJcblxyXG5jb25zdCByZWFjdFBsdWdpbiA9IG5ldyBSZWFjdFBsdWdpbigpO1xyXG5jb25zdCBhcHBJbnNpZ2h0cyA9IG5ldyBBcHBsaWNhdGlvbkluc2lnaHRzKHtcclxuICBjb25maWc6IHtcclxuICAgIGNvbm5lY3Rpb25TdHJpbmc6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQUElOU0lHSFRTX0NPTk5FQ1RJT05fU1RSSU5HLFxyXG4gICAgZW5hYmxlQXV0b1JvdXRlVHJhY2tpbmc6IHRydWUsXHJcbiAgICBleHRlbnNpb25zOiBbcmVhY3RQbHVnaW5dLFxyXG4gIH0sXHJcbn0pO1xyXG5cclxuYXBwSW5zaWdodHMubG9hZEFwcEluc2lnaHRzKCk7XHJcblxyXG5leHBvcnQgeyBhcHBJbnNpZ2h0cywgcmVhY3RQbHVnaW4gfTtcclxuIl0sIm5hbWVzIjpbIkFwcGxpY2F0aW9uSW5zaWdodHMiLCJSZWFjdFBsdWdpbiIsInJlYWN0UGx1Z2luIiwiYXBwSW5zaWdodHMiLCJjb25maWciLCJjb25uZWN0aW9uU3RyaW5nIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQUElOU0lHSFRTX0NPTk5FQ1RJT05fU1RSSU5HIiwiZW5hYmxlQXV0b1JvdXRlVHJhY2tpbmciLCJleHRlbnNpb25zIiwibG9hZEFwcEluc2lnaHRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./src/lib/app-insights.ts\n");

/***/ }),

/***/ "(api)/./src/lib/interceptors/axios-client.ts":
/*!**********************************************!*\
  !*** ./src/lib/interceptors/axios-client.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   onError: () => (/* binding */ onError),\n/* harmony export */   onRequest: () => (/* binding */ onRequest),\n/* harmony export */   onResponse: () => (/* binding */ onResponse)\n/* harmony export */ });\n/* harmony import */ var _app_insights__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../app-insights */ \"(api)/./src/lib/app-insights.ts\");\n\nconst onRequest = (config)=>{\n    _app_insights__WEBPACK_IMPORTED_MODULE_0__.appInsights.trackEvent({\n        name: \"HTTP Request\",\n        properties: {\n            url: config.url,\n            method: config.method\n        }\n    });\n    return config;\n};\nconst onResponse = (response)=>{\n    _app_insights__WEBPACK_IMPORTED_MODULE_0__.appInsights.trackEvent({\n        name: \"HTTP Response\",\n        properties: {\n            url: response.config.url,\n            status: response.status\n        }\n    });\n    if (response.status === 504) {\n        throw new Error(`Gateway Timeout for URL: ${response.config.url}`);\n    }\n    return response;\n};\nconst onError = (error)=>{\n    _app_insights__WEBPACK_IMPORTED_MODULE_0__.appInsights.trackException({\n        error\n    });\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    error.config.metadata = {\n        time: new Date()\n    };\n    return Promise.reject(error);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/interceptors/axios-client.ts\n");

/***/ }),

/***/ "(api)/./src/lib/with-session.ts":
/*!*********************************!*\
  !*** ./src/lib/with-session.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withSessionApiRoute: () => (/* binding */ withSessionApiRoute),\n/* harmony export */   withSessionSsr: () => (/* binding */ withSessionSsr)\n/* harmony export */ });\n/* harmony import */ var iron_session_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! iron-session/next */ \"iron-session/next\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([iron_session_next__WEBPACK_IMPORTED_MODULE_0__]);\niron_session_next__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n//import { CookieSerializeOptions } from 'next/dist/server/web/types';\nconst defaultTtl = 60 * 60;\nconst cookieOptions = {\n    httpOnly: \"development\" === \"production\",\n    sameSite: \"strict\",\n    secure: true,\n    maxAge: defaultTtl\n};\nconst sessionOptions = {\n    cookieName: \"anon_session\",\n    password: process.env.IRON_SESSION_SECRET,\n    ttl: defaultTtl,\n    cookieOptions\n};\nfunction withSessionApiRoute(handler) {\n    return (0,iron_session_next__WEBPACK_IMPORTED_MODULE_0__.withIronSessionApiRoute)(handler, sessionOptions);\n}\nfunction withSessionSsr(handler) {\n    // return async (context) => {\n    //   const authToken = await getCookie('AuthToken', { req: context.req, res: context.res });\n    //   const decodedToken = jwt.decode(authToken as string);\n    //   const ttl =\n    //     decodedToken && typeof decodedToken !== 'string' && decodedToken.exp\n    //       ? decodedToken.exp - Math.floor(Date.now() / 1000)\n    //       : defaultTtl;\n    //   const dynamicSession: IronSessionOptions = {\n    //     ...sessionOptions,\n    //     ttl,\n    //     cookieOptions: {\n    //       ...cookieOptions,\n    //       maxAge: ttl,\n    //     },\n    //   };\n    //   return withIronSessionSsr(handler, dynamicSession)(context);\n    // };\n    return (0,iron_session_next__WEBPACK_IMPORTED_MODULE_0__.withIronSessionSsr)(handler, sessionOptions);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/with-session.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/myaccount/paperlessbilling/index.ts":
/*!***********************************************************!*\
  !*** ./src/pages/api/myaccount/paperlessbilling/index.ts ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var lib_with_session__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lib/with-session */ \"(api)/./src/lib/with-session.ts\");\n/* harmony import */ var src_services_MyAccountAPI__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/services/MyAccountAPI */ \"(api)/./src/services/MyAccountAPI/index.ts\");\n/* harmony import */ var src_utils_util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/utils/util */ \"(api)/./src/utils/util.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([lib_with_session__WEBPACK_IMPORTED_MODULE_0__, src_services_MyAccountAPI__WEBPACK_IMPORTED_MODULE_1__, src_utils_util__WEBPACK_IMPORTED_MODULE_2__]);\n([lib_with_session__WEBPACK_IMPORTED_MODULE_0__, src_services_MyAccountAPI__WEBPACK_IMPORTED_MODULE_1__, src_utils_util__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nasync function handler(req, res) {\n    const auth_token = req.session.user?.access_token;\n    if (auth_token) {\n        switch(req.method){\n            case \"POST\":\n                {\n                    try {\n                        const body = req.body;\n                        const transferRes = await src_services_MyAccountAPI__WEBPACK_IMPORTED_MODULE_1__[\"default\"].setPaperlessBilling(body, auth_token);\n                        res.status(200).json(transferRes.data);\n                    } catch (error) {\n                        const errorcheck = await (0,src_utils_util__WEBPACK_IMPORTED_MODULE_2__.ErrorReturn)(error);\n                        res.status(500).send(errorcheck);\n                    }\n                }\n            case \"GET\":\n                {\n                    try {\n                        // const ca = req.body.contractAccountNumber as string;\n                        const { ca } = req.query;\n                        const transferRes = await src_services_MyAccountAPI__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getPaperlessBilling(ca, auth_token);\n                        res.status(200).json(transferRes.data);\n                    } catch (error) {\n                        const errorcheck = await (0,src_utils_util__WEBPACK_IMPORTED_MODULE_2__.ErrorReturn)(error);\n                        res.status(500).send(errorcheck);\n                    }\n                }\n            default:\n                {\n                    res.status(405).end();\n                }\n        }\n    } else {\n        res.status(401).end();\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,lib_with_session__WEBPACK_IMPORTED_MODULE_0__.withSessionApiRoute)(handler));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/myaccount/paperlessbilling/index.ts\n");

/***/ }),

/***/ "(api)/./src/services/BaseServiceAPI/axiosCustomInstance.ts":
/*!************************************************************!*\
  !*** ./src/services/BaseServiceAPI/axiosCustomInstance.ts ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios_1_4__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios-1.4 */ \"axios-1.4\");\n/* harmony import */ var lib_interceptors_axios_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lib/interceptors/axios-client */ \"(api)/./src/lib/interceptors/axios-client.ts\");\n/* harmony import */ var https__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! https */ \"https\");\n/* harmony import */ var https__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(https__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var http__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! http */ \"http\");\n/* harmony import */ var http__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(http__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios_1_4__WEBPACK_IMPORTED_MODULE_0__]);\naxios_1_4__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nclass AxiosCustomInstance {\n    static getInstance() {\n        if (!AxiosCustomInstance.axiosInstance) {\n            AxiosCustomInstance.axiosInstance = axios_1_4__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n                timeout: 30000,\n                httpAgent: new http__WEBPACK_IMPORTED_MODULE_3__.Agent({\n                    keepAlive: true\n                }),\n                httpsAgent: new https__WEBPACK_IMPORTED_MODULE_2__.Agent({\n                    keepAlive: true,\n                    maxVersion: \"TLSv1.2\",\n                    minVersion: \"TLSv1.2\"\n                })\n            });\n            AxiosCustomInstance.axiosInstance.interceptors.request.use(lib_interceptors_axios_client__WEBPACK_IMPORTED_MODULE_1__.onRequest, lib_interceptors_axios_client__WEBPACK_IMPORTED_MODULE_1__.onError);\n            AxiosCustomInstance.axiosInstance.interceptors.response.use(lib_interceptors_axios_client__WEBPACK_IMPORTED_MODULE_1__.onResponse, lib_interceptors_axios_client__WEBPACK_IMPORTED_MODULE_1__.onError);\n        }\n        return AxiosCustomInstance.axiosInstance;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AxiosCustomInstance);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/services/BaseServiceAPI/axiosCustomInstance.ts\n");

/***/ }),

/***/ "(api)/./src/services/MyAccountAPI/index.ts":
/*!********************************************!*\
  !*** ./src/services/MyAccountAPI/index.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../BaseServiceAPI/axiosCustomInstance */ \"(api)/./src/services/BaseServiceAPI/axiosCustomInstance.ts\");\n/* harmony import */ var _endpoints_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../endpoints.json */ \"(api)/./src/services/endpoints.json\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__]);\n_BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nconst MyAccountAPI = {\n    getUsageOverview: async (esiid, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().get(`${_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.getUsageOverview}/${esiid}`, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    getOffers: async (body, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().post(_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.retGetOffers, body, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    getExistingPlan: async (partnerNumber, accountNumber, esiid, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().get(`${_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.existingPlan}/${partnerNumber}/${accountNumber}/${esiid}`, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    getKYPEligiblity: async (bpNumber, esiid, planid, access_token)=>{\n        const endpoint = _endpoints_json__WEBPACK_IMPORTED_MODULE_1__.getKYPEligiblity.replace(\"{bp}\", bpNumber);\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().get(`${endpoint}/${esiid}/${planid}`, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    getCustomerData: async (partnerNumber, accountNumber, esiid, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().get(`${_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.getCustomerdata}/${partnerNumber}/${accountNumber}/${esiid}`, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    getProductRateList: async (ProductID, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().post(`${_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.getProductRateList}`, ProductID, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    getPaperlessBilling: async (contractaccount, authToken)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().get(_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.getPaperlessBilling + \"/\" + contractaccount, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${authToken}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    setPaperlessBilling: async (body, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().post(_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.setPaperlessBilling, body, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    setPaperlessEmail: async (body, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().post(_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.setPaperlessEmail, body, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    setSecondaryAccountHolder: async (body, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().post(_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.setSecondaryAccountHolder, body, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    updateAddBillingAddress: async (body, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().post(_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.updateAddBillingAddress, body, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    getConnectDate: async (esiid, intent, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().get(`${_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.getMyAccountConnectDate}/${intent}/${esiid}`, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    getMeterReadDates: async (esiid, partnerNumber, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().get(`${_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.getMeterReadDates}/${esiid}/${partnerNumber}` + \"/\" + \"enrollment\", {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    createSwap: async (body, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().post(_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.createSwap, body, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    getTargettedRenewal: async (bpnumber, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().get(`${_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.IsTargettedRenewal}/${bpnumber}`, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    switchHold: async (body, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().post(_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.transferSwitchHold, body, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    },\n    getPlanInformation: async (partnerNumber, accountNumber, esiid, access_token)=>{\n        return _BaseServiceAPI_axiosCustomInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().get(`${_endpoints_json__WEBPACK_IMPORTED_MODULE_1__.getPlanInformation}/${partnerNumber}/${accountNumber}/${esiid}`, {\n            baseURL: process.env.AWS_EKS_URL,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${access_token}`,\n                BrandId: process.env.BrandValue\n            }\n        });\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyAccountAPI);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/services/MyAccountAPI/index.ts\n");

/***/ }),

/***/ "(api)/./src/utils/util.ts":
/*!***************************!*\
  !*** ./src/utils/util.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DecodeURL: () => (/* binding */ DecodeURL),\n/* harmony export */   ErrorReturn: () => (/* binding */ ErrorReturn),\n/* harmony export */   FormatPhoneNumber: () => (/* binding */ FormatPhoneNumber),\n/* harmony export */   FormattedDate: () => (/* binding */ FormattedDate),\n/* harmony export */   decryptURL: () => (/* binding */ decryptURL),\n/* harmony export */   getANumber: () => (/* binding */ getANumber),\n/* harmony export */   isAMB: () => (/* binding */ isAMB),\n/* harmony export */   isMac: () => (/* binding */ isMac),\n/* harmony export */   isTxu: () => (/* binding */ isTxu),\n/* harmony export */   removeURLParams: () => (/* binding */ removeURLParams)\n/* harmony export */ });\n/* harmony import */ var hashids__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hashids */ \"hashids\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([hashids__WEBPACK_IMPORTED_MODULE_0__]);\nhashids__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nfunction removeURLParams(url) {\n    if (url.includes(\"?\")) {\n        const newURL = url.slice(0, url.indexOf(\"?\"));\n        return newURL;\n    }\n    return url;\n}\n// detect if the user is on a MacOS device\nconst isMac = ()=>{\n    if (typeof navigator === \"undefined\") {\n        return false;\n    }\n    if (navigator.userAgent) {\n        // Check using userAgent\n        return navigator.userAgent.toLowerCase().includes(\"mac\");\n    }\n    return navigator.userAgent.toLowerCase().includes(\"mac\");\n};\nconst isTxu = \"\" === \"txu\";\nconst isAMB = \"\" === \"amb\";\nconst hashids = new hashids__WEBPACK_IMPORTED_MODULE_0__[\"default\"](\"Secret\", 6);\nconst decryptURL = (hash)=>{\n    // const decoded = hashids.decode(hash);\n    // const first = decoded[0];\n    // if (typeof first === 'number') {\n    //   return first;\n    // }\n    return hash;\n};\nconst FormattedDate = (date)=>{\n    const unformattedDate = new Date(date);\n    const day = String(unformattedDate.getDate()).padStart(2, \"0\");\n    const month = String(unformattedDate.getMonth() + 1).padStart(2, \"0\"); // Month is zero-based\n    const year = unformattedDate.getFullYear();\n    return `${month}/${day}/${year}`;\n};\nconst FormatPhoneNumber = (phoneNumber)=>{\n    const cleaned = phoneNumber?.toString()?.replace(/\\D/g, \"\");\n    return cleaned.replace(/^(\\d{3})(\\d{3})(\\d{4})$/, \"($1) $2-$3\");\n};\nconst DecodeURL = (encodedUrl)=>{\n    let fullUrl;\n    try {\n        // This will throw if encodedUrl is a relative path\n        new URL(encodedUrl);\n        fullUrl = encodedUrl; // already has origin\n    } catch  {\n        // Relative URL, so prepend origin\n        fullUrl = `${window.location.origin}${encodedUrl}`;\n    }\n    const decoded = decodeURIComponent(fullUrl);\n    return decoded;\n};\nconst ErrorReturn = (err)=>{\n    if (process.env.NEXT_ERROR_PROPERTY === \"true\") {\n        const error = {\n            ...err.customData\n        };\n        return error;\n    } else {\n        return err;\n    }\n};\nconst getANumber = (anumber)=>{\n    const ano = anumber?.toString();\n    return ano;\n// const ano = anumber?.toString();\n// if (ano !== undefined && ano !== '') {\n//   return ano?.startsWith('a', 0) ? ano?.replace(ano[0], 'A') : 'A' + ano;\n// } else {\n//   return '';\n// }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/utils/util.ts\n");

/***/ }),

/***/ "(api)/./src/services/endpoints.json":
/*!*************************************!*\
  !*** ./src/services/endpoints.json ***!
  \*************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"getAccessToken":"/identity/connect/token","addressSearch":"/Prod/cloudsearch-data","getPlansurl":"/handlers/getplans.ashx","getNCPlanurl":"/handlers/getnoncommodityplan.ashx","getEVList":"/ev/EVEndpoints/getEVList","getEVIn":"/ev/EVEndpoints/getfordvehiclevin","getDocumentsLink":"/handlers/PDFGenerator.ashx?comProdId={{0}}&lang={{1}}&formType={{2}}&custClass={{3}}&tdsp={{4}}","getPdfViewer":"/myaccount/billing/view/document/{arcid}/{docid}","getDocument":"/shopping/document?docType={docType}&productId={productId}&EFLDate={EFLDate}&tdsp={tdsp}&language={language}&Classification={Classification}&pseudoQuoteId={pseudoQuoteId}","createVirtualUser":"/UserLogin/CreateVirtualUserFromShoppingLogin","logoutVirtualUser":"/UserLogin/LogoutFromShopping","zipSearch":"/Prod/cloudsearch-zip","sunRunZipSearch":"/Prod/cloudsearch-sunrunzip","getPaymetricAccessToken":"/AccessToken","getResponsePacket":"/ResponsePacket","getLPAccessToken":"/digitalauthservice/login","getOffers":"/shopping/plan/product/offers","getConnectDate":"/shopping/connect/cal","customer":"/shopping/create/customer","securityDepositCheck":"/shopping/security/deposit/check","calcuateDeposit":"/shopping/customer/deposit/calculate","account":"/shopping/account","connectValidate":"/shopping/connect/validate","getSolutionProduct":"/shopping/plan/solution/offers","orderSolutionProduct":"/shopping/plan/order/noncommodity","createOnlineAccount":"/shopping/profile/create/account","connect":"/shopping/connect","paySecurityDeposit":"/shopping/deposit/security/card","makePriorDebtPaymentCard":"/shopping/payment/priordebt/card","autoPay":"/shopping/payment/autopay","scheduleRemainingDeposit":"/shopping/payment/schedule/remaining/deposit","getProductDeposit":"/shopping/products/deposit","sendOTP":"/shopping/oow/sendotp","validateOTP":"/shopping/oow/otpvalidation","validateKIQ":"/shopping/oow/kiqvalidation","checkfraudandtdValidation":"/shopping/check/fraud","checkUserByEmail":"/myaccount/userprofile/validate/userbyemail","checkfraud":"/shopping/fraud","eLeaseEmailConfirmation":"/shopping/email/confirmation","helpMeChoose":"/shopping/txu/persondalization","helpMeChooseMyAccount":"/myaccount/shopping/txu/persondalization","offlineEnrollment":"/shopping/offline/enrollment","paymentlocations":"/shopping/payment/location/{latitude}/{longitude}/{distance}","setSecondaryAccountHolder":"/myaccount/shopping/set/secondary/user","getPendingTransactionStatusForAnynomousUser":"/shopping/pending/transcation/status","checkUser":"/shopping/{username}/check","getCharityCode":"/shopping/charity/codes","saveCharityCode":"/shopping/charity/save","setCommPreferences":"/shopping/set/preferences","createCustomerNext":"/api/customer","connectNext":"/api/customer/connect","getUserProfile":"/myaccount/userprofile/extended","validateUserName":"/myaccount/check/account","existingPlan":"/myaccount/shopping/details","getContractAccount":"/myaccount/shopping/get/accounts","getEsiids":"/myaccount/shopping/get/esiids","getTransferConnectDate":"/myaccount/shopping/connect/cal/Transfer","getTransferDisConnectDate":"/myaccount/shopping/connect/cal/MoveOut","checkTransferEligibility":"/myaccount/shopping/transfer/eligibility","transfer":"/myaccount/shopping/transfer","getCustomerdata":"/myaccount/shopping/customer/data","transferBillingAddress":"/myaccount/set/mailing/address","getProductRateList":"/myaccount/shopping/plan/product/rates","getPaperlessBilling":"/myaccount/shopping/get/paperless","setPaperlessBilling":"/myaccount/shopping/billing/paperlessbilling/status","updateAddBillingAddress":"/myaccount/update/billing/address","addCreateContractAccount":"/myaccount/shopping/account","getFraud":"/myaccount/shopping/get/fraud","addConnectValidate":"/myaccount/shopping/connect/validate","addConnect":"/myaccount/shopping/connect","retGetOffers":"/myaccount/shopping/plan/product/offers","getMyAccountConnectDate":"/myaccount/shopping/connect/cal","getMeterReadDates":"/myaccount/shopping/meter/dates","createSwap":"/myaccount/shopping/swap","getSolutionProductForAuthenticatedUser":"/myaccount/shopping/plan/solution/offers","orderSolutionProductForAuthenticatedUser":"/myaccount/shopping/plan/order/noncommodity","transferSwitchHold":"/myaccount/shopping/switch/hold/status","setPaperlessEmail":"/myaccount/shopping/set/email","getPendingTransactionStatus":"/myaccount/shopping/pending/transcation/status","getUsageOverview":"/myaccount/shopping/consumption/usage","IsTargettedRenewal":"/myaccount/shopping/residential/targettedRenewal","getCustomerDetails":"/myaccount/shopping/customer/details/{ContractAccountNumber}","getKYPEligiblity":"/myaccount/shopping/kyp/{bp}/eligibility","updateBillingAddress":"/myaccount/customer/update/billing/address","getPlanInformation":"/myaccount/plan/information"}');

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmyaccount%2Fpaperlessbilling&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cmyaccount%5Cpaperlessbilling%5Cindex.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();