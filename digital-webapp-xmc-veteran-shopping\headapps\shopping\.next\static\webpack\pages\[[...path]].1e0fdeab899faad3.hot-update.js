"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[[...path]]",{

/***/ "./src/components/MyAccount/Add/AddAccountSetup/AddAccountSetup.tsx":
/*!**************************************************************************!*\
  !*** ./src/components/MyAccount/Add/AddAccountSetup/AddAccountSetup.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddAccountSetup: function() { return /* binding */ AddAccountSetup; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mantine/core */ \"./node_modules/@mantine/core/esm/index.js\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss-nextjs */ \"./node_modules/@sitecore-jss/sitecore-jss-nextjs/dist/esm/index.js\");\n/* harmony import */ var axios_1_4__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios-1.4 */ \"./node_modules/axios-1.4/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/pro-light-svg-icons */ \"./node_modules/@fortawesome/pro-light-svg-icons/index.mjs\");\n/* harmony import */ var src_stores_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/stores/store */ \"./src/stores/store.ts\");\n/* harmony import */ var src_utils_util__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/utils/util */ \"./src/utils/util.ts\");\n\r\nvar _s = $RefreshSig$();\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst AddAccountSetup = (props)=>{\r\n    var _props_fields_Title, _props_fields, _props_fields_UseExistingAccountText, _props_fields1, _props_fields_UseExistingAccountDescription, _props_fields2, _props_fields_UseSeparateAccountText, _props_fields3;\r\n    _s();\r\n    const context = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.useSitecoreContext)();\r\n    const isPageEditing = context.sitecoreContext.pageEditing;\r\n    let bpNumber = undefined;\r\n    if (!isPageEditing) {\r\n        bpNumber = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_3__.useAppSelector)((state)=>{\r\n            var _state_authuser;\r\n            return (_state_authuser = state.authuser) === null || _state_authuser === void 0 ? void 0 : _state_authuser.bpNumber;\r\n        });\r\n    }\r\n    const [contractAccounts, setContractAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\r\n        const activeContractaccounts = [];\r\n        const accountselector = async ()=>{\r\n            const accountSelectorRequest = {\r\n                PartnerNumber: bpNumber,\r\n                Status: \"Active\",\r\n                ContractAccount: \"\"\r\n            };\r\n            const cas = await axios_1_4__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"/api/accountselector/contractaccount\", accountSelectorRequest);\r\n            if (cas.data != undefined && cas.data.result.length > 0) {\r\n                cas.data.result.map((ca)=>{\r\n                    if (ca.accountStatus === \"Active\") {\r\n                        activeContractaccounts.push(ca.contractAccount);\r\n                    }\r\n                });\r\n                if (activeContractaccounts.length === 1) {\r\n                    props.form.setFieldValue(\"contractAccount\", activeContractaccounts[0]);\r\n                }\r\n                setContractAccounts([\r\n                    ...activeContractaccounts\r\n                ]);\r\n            }\r\n        };\r\n        if (bpNumber) {\r\n            accountselector();\r\n        }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [\r\n        bpNumber\r\n    ]);\r\n    // fix\r\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n        className: \"flex flex-col gap-8\",\r\n        children: [\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.Text, {\r\n                tag: \"p\",\r\n                className: \"text-base sm:text-plus2 font-primaryBlack text-textQuattuordenary\",\r\n                field: {\r\n                    value: (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_Title = _props_fields.Title) === null || _props_fields_Title === void 0 ? void 0 : _props_fields_Title.value\r\n                }\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddAccountSetup\\\\AddAccountSetup.tsx\",\r\n                lineNumber: 70,\r\n                columnNumber: 7\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Radio.Group, {\r\n                ...props.form.getInputProps(\"isExistingBill\"),\r\n                styles: ()=>{\r\n                    if (!src_utils_util__WEBPACK_IMPORTED_MODULE_4__.isTxu) return {\r\n                        root: {\r\n                            display: \"flex\",\r\n                            flexDirection: \"column\",\r\n                            gap: \"18px\",\r\n                            label: {\r\n                                fontFamily: \"OpenSans-Regular\"\r\n                            }\r\n                        }\r\n                    };\r\n                    else return {\r\n                        root: {\r\n                            display: \"flex\",\r\n                            flexDirection: \"column\",\r\n                            gap: \"18px\"\r\n                        }\r\n                    };\r\n                },\r\n                children: [\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Radio, {\r\n                        value: \"true\",\r\n                        label: (_props_fields1 = props.fields) === null || _props_fields1 === void 0 ? void 0 : (_props_fields_UseExistingAccountText = _props_fields1.UseExistingAccountText) === null || _props_fields_UseExistingAccountText === void 0 ? void 0 : _props_fields_UseExistingAccountText.value\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddAccountSetup\\\\AddAccountSetup.tsx\",\r\n                        lineNumber: 93,\r\n                        columnNumber: 9\r\n                    }, undefined),\r\n                    props.form.values.isExistingBill === \"true\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\r\n                        children: [\r\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Select, {\r\n                                data: contractAccounts,\r\n                                ...props.form.getInputProps(\"contractAccount\"),\r\n                                styles: {\r\n                                    root: {\r\n                                        width: \"280px\",\r\n                                        [\"@media (max-width: 640px)\"]: {\r\n                                            width: \"100%\"\r\n                                        }\r\n                                    }\r\n                                },\r\n                                rightSection: contractAccounts.length > 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\r\n                                    icon: _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_8__.faChevronDown,\r\n                                    className: \"text-digitalBlueBonnet text-textSecondary hover:text-textPrimary\"\r\n                                }, void 0, false, {\r\n                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddAccountSetup\\\\AddAccountSetup.tsx\",\r\n                                    lineNumber: 109,\r\n                                    columnNumber: 19\r\n                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\r\n                                rightSectionProps: {\r\n                                    style: {\r\n                                        marginRight: \"8px\"\r\n                                    }\r\n                                },\r\n                                readOnly: contractAccounts.length <= 1\r\n                            }, void 0, false, {\r\n                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddAccountSetup\\\\AddAccountSetup.tsx\",\r\n                                lineNumber: 96,\r\n                                columnNumber: 13\r\n                            }, undefined),\r\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.Text, {\r\n                                tag: \"p\",\r\n                                className: \"text-minus2 text-textQuattuordenary font-primaryRegular \",\r\n                                field: {\r\n                                    value: (_props_fields2 = props.fields) === null || _props_fields2 === void 0 ? void 0 : (_props_fields_UseExistingAccountDescription = _props_fields2.UseExistingAccountDescription) === null || _props_fields_UseExistingAccountDescription === void 0 ? void 0 : _props_fields_UseExistingAccountDescription.value\r\n                                }\r\n                            }, void 0, false, {\r\n                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddAccountSetup\\\\AddAccountSetup.tsx\",\r\n                                lineNumber: 120,\r\n                                columnNumber: 13\r\n                            }, undefined)\r\n                        ]\r\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Radio, {\r\n                        value: \"false\",\r\n                        label: (_props_fields3 = props.fields) === null || _props_fields3 === void 0 ? void 0 : (_props_fields_UseSeparateAccountText = _props_fields3.UseSeparateAccountText) === null || _props_fields_UseSeparateAccountText === void 0 ? void 0 : _props_fields_UseSeparateAccountText.value\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddAccountSetup\\\\AddAccountSetup.tsx\",\r\n                        lineNumber: 129,\r\n                        columnNumber: 9\r\n                    }, undefined)\r\n                ]\r\n            }, void 0, true, {\r\n                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddAccountSetup\\\\AddAccountSetup.tsx\",\r\n                lineNumber: 75,\r\n                columnNumber: 7\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\r\n                className: \"border-borderNonary border-[1px] w-full sm:w-[636px]\"\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddAccountSetup\\\\AddAccountSetup.tsx\",\r\n                lineNumber: 131,\r\n                columnNumber: 7\r\n            }, undefined)\r\n        ]\r\n    }, void 0, true, {\r\n        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddAccountSetup\\\\AddAccountSetup.tsx\",\r\n        lineNumber: 69,\r\n        columnNumber: 5\r\n    }, undefined);\r\n};\r\n_s(AddAccountSetup, \"Q7JFUkVNtXln8E9t50AQnH6OHRg=\", false, function() {\r\n    return [\r\n        _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.useSitecoreContext\r\n    ];\r\n});\r\n_c = AddAccountSetup;\r\n\r\n// const Component = withDatasourceCheck()<AddAccountSetupProps>(AddAccountSetup);\r\n// export default aiLogger(Component, Component.name);\r\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddAccountSetup);\r\nvar _c;\r\n$RefreshReg$(_c, \"AddAccountSetup\");\r\n\r\n\r\n;\r\n    // Wrapped in an IIFE to avoid polluting the global scope\r\n    ;\r\n    (function () {\r\n        var _a, _b;\r\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\r\n        // to extract CSS. For backwards compatibility, we need to check we're in a\r\n        // browser context before continuing.\r\n        if (typeof self !== 'undefined' &&\r\n            // AMP / No-JS mode does not inject these helpers:\r\n            '$RefreshHelpers$' in self) {\r\n            // @ts-ignore __webpack_module__ is global\r\n            var currentExports = module.exports;\r\n            // @ts-ignore __webpack_module__ is global\r\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\r\n            // This cannot happen in MainTemplate because the exports mismatch between\r\n            // templating and execution.\r\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\r\n            // A module can be accepted automatically based on its exports, e.g. when\r\n            // it is a Refresh Boundary.\r\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\r\n                // Save the previous exports signature on update so we can compare the boundary\r\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\r\n                module.hot.dispose(function (data) {\r\n                    data.prevSignature =\r\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\r\n                });\r\n                // Unconditionally accept an update to this module, we'll check if it's\r\n                // still a Refresh Boundary later.\r\n                // @ts-ignore importMeta is replaced in the loader\r\n                module.hot.accept();\r\n                // This field is set when the previous version of this module was a\r\n                // Refresh Boundary, letting us know we need to check for invalidation or\r\n                // enqueue an update.\r\n                if (prevSignature !== null) {\r\n                    // A boundary can become ineligible if its exports are incompatible\r\n                    // with the previous exports.\r\n                    //\r\n                    // For example, if you add/remove/change exports, we'll want to\r\n                    // re-execute the importing modules, and force those components to\r\n                    // re-render. Similarly, if you convert a class component to a\r\n                    // function, we want to invalidate the boundary.\r\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\r\n                        module.hot.invalidate();\r\n                    }\r\n                    else {\r\n                        self.$RefreshHelpers$.scheduleUpdate();\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                // Since we just executed the code for the module, it's possible that the\r\n                // new exports made it ineligible for being a boundary.\r\n                // We only care about the case when we were _previously_ a boundary,\r\n                // because we already accepted this update (accidental side effect).\r\n                var isNoLongerABoundary = prevSignature !== null;\r\n                if (isNoLongerABoundary) {\r\n                    module.hot.invalidate();\r\n                }\r\n            }\r\n        }\r\n    })();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/MyAccount/Add/AddAccountSetup/AddAccountSetup.tsx\n"));

/***/ })

});