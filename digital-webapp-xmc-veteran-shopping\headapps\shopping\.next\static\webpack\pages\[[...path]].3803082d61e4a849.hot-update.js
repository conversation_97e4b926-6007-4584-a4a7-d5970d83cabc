"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[[...path]]",{

/***/ "./src/components/MyAccount/Add/AddBillingInfo/AddBillingInfo.tsx":
/*!************************************************************************!*\
  !*** ./src/components/MyAccount/Add/AddBillingInfo/AddBillingInfo.tsx ***!
  \************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddBillingInfo: function() { return /* binding */ AddBillingInfo; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @fortawesome/pro-light-svg-icons */ \"./node_modules/@fortawesome/pro-light-svg-icons/index.mjs\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"./node_modules/@mantine/core/esm/index.js\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss-nextjs */ \"./node_modules/@sitecore-jss/sitecore-jss-nextjs/dist/esm/index.js\");\n/* harmony import */ var assets_icons_QuestionCircle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! assets/icons/QuestionCircle */ \"./src/assets/icons/QuestionCircle.tsx\");\n/* harmony import */ var axios_1_4__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! axios-1.4 */ \"./node_modules/axios-1.4/index.js\");\n/* harmony import */ var components_Elements_Tooltip_Tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! components/Elements/Tooltip/Tooltip */ \"./src/components/Elements/Tooltip/Tooltip.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var src_stores_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/stores/store */ \"./src/stores/store.ts\");\n/* harmony import */ var src_stores_planSlice__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/stores/planSlice */ \"./src/stores/planSlice.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-redux */ \"./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var src_utils_util__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/utils/util */ \"./src/utils/util.ts\");\n\r\nvar _s = $RefreshSig$();\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst AddBillingInfo = (props)=>{\r\n    var _props_fields_EVDisclaimer_fields_EVModels_value, _props_fields_EVDisclaimer_fields_EVModels, _props_fields_EVDisclaimer_fields, _props_fields_EVDisclaimer, _props_fields_EVDisclaimer_fields_EVDisclaimerMessage, _props_fields_EVDisclaimer_fields1, _props_fields_EVDisclaimer1;\r\n    _s();\r\n    const context = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_8__.useSitecoreContext)();\r\n    const isPageEditing = context.sitecoreContext.pageEditing;\r\n    let ev = undefined;\r\n    let dispatch;\r\n    if (!isPageEditing) {\r\n        ev = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_5__.useAppSelector)((state)=>{\r\n            var _state_plans;\r\n            return (_state_plans = state.plans) === null || _state_plans === void 0 ? void 0 : _state_plans.selectedPlan.ev;\r\n        });\r\n        dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_9__.useDispatch)();\r\n    }\r\n    const [isSameAddress, setIsSameAddress] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\r\n    const [isPoBox, setIsPoBox] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\r\n    const isSecondaryAccountHolder = props.form.values.isSecondaryAccountHolder;\r\n    const isPaperlessBilling = props.form.values.isPaperlessBilling;\r\n    const [paperlessStatus, setPaperlessStatus] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({});\r\n    const isEV = ev && ((_props_fields_EVDisclaimer = props.fields.EVDisclaimer) === null || _props_fields_EVDisclaimer === void 0 ? void 0 : (_props_fields_EVDisclaimer_fields = _props_fields_EVDisclaimer.fields) === null || _props_fields_EVDisclaimer_fields === void 0 ? void 0 : (_props_fields_EVDisclaimer_fields_EVModels = _props_fields_EVDisclaimer_fields.EVModels) === null || _props_fields_EVDisclaimer_fields_EVModels === void 0 ? void 0 : (_props_fields_EVDisclaimer_fields_EVModels_value = _props_fields_EVDisclaimer_fields_EVModels.value) === null || _props_fields_EVDisclaimer_fields_EVModels_value === void 0 ? void 0 : _props_fields_EVDisclaimer_fields_EVModels_value.includes(ev));\r\n    const [isEVDisclaimerSelected, setIsEVDisclaimerSelected] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\r\n    const [custMaillingAddress, setCustMaillingAddress] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\r\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\r\n        if (!isPageEditing) {\r\n            var _props_fields_EVDisclaimer_fields_EVDisclaimerMessage, _props_fields_EVDisclaimer_fields, _props_fields_EVDisclaimer, _props_fields_EVDisclaimer_fields_EVDisclaimerMessage1, _props_fields_EVDisclaimer_fields1, _props_fields_EVDisclaimer1;\r\n            dispatch((0,src_stores_planSlice__WEBPACK_IMPORTED_MODULE_6__.setEV)({\r\n                isEVSelected: isEVDisclaimerSelected,\r\n                EVDisclaimerMessage: (_props_fields_EVDisclaimer = props.fields.EVDisclaimer) === null || _props_fields_EVDisclaimer === void 0 ? void 0 : (_props_fields_EVDisclaimer_fields = _props_fields_EVDisclaimer.fields) === null || _props_fields_EVDisclaimer_fields === void 0 ? void 0 : (_props_fields_EVDisclaimer_fields_EVDisclaimerMessage = _props_fields_EVDisclaimer_fields.EVDisclaimerMessage) === null || _props_fields_EVDisclaimer_fields_EVDisclaimerMessage === void 0 ? void 0 : _props_fields_EVDisclaimer_fields_EVDisclaimerMessage.value,\r\n                DiclaimerErrorText: \"\",\r\n                EVModels: (_props_fields_EVDisclaimer1 = props.fields.EVDisclaimer) === null || _props_fields_EVDisclaimer1 === void 0 ? void 0 : (_props_fields_EVDisclaimer_fields1 = _props_fields_EVDisclaimer1.fields) === null || _props_fields_EVDisclaimer_fields1 === void 0 ? void 0 : (_props_fields_EVDisclaimer_fields_EVDisclaimerMessage1 = _props_fields_EVDisclaimer_fields1.EVDisclaimerMessage) === null || _props_fields_EVDisclaimer_fields_EVDisclaimerMessage1 === void 0 ? void 0 : _props_fields_EVDisclaimer_fields_EVDisclaimerMessage1.value\r\n            }));\r\n        }\r\n    }, [\r\n        dispatch,\r\n        isPageEditing,\r\n        isEVDisclaimerSelected,\r\n        (_props_fields_EVDisclaimer1 = props.fields.EVDisclaimer) === null || _props_fields_EVDisclaimer1 === void 0 ? void 0 : (_props_fields_EVDisclaimer_fields1 = _props_fields_EVDisclaimer1.fields) === null || _props_fields_EVDisclaimer_fields1 === void 0 ? void 0 : (_props_fields_EVDisclaimer_fields_EVDisclaimerMessage = _props_fields_EVDisclaimer_fields1.EVDisclaimerMessage) === null || _props_fields_EVDisclaimer_fields_EVDisclaimerMessage === void 0 ? void 0 : _props_fields_EVDisclaimer_fields_EVDisclaimerMessage.value\r\n    ]);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\r\n        async function getPaperlessStatus() {\r\n            const paperlessReq = await axios_1_4__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"/api/myaccount/paperlessbilling\", {\r\n                params: {\r\n                    ca: contractAccount\r\n                }\r\n            });\r\n            if (paperlessReq.data) {\r\n                props.form.setFieldValue(\"isPaperlessBilling\", paperlessReq.data.result.isPaperLess);\r\n                if (paperlessReq.data.result.isPaperLess) {\r\n                    props.form.setFieldValue(\"paperlessBillingEmail\", paperlessReq.data.result.userEmail);\r\n                } else {\r\n                    props.form.setFieldValue(\"paperlessBillingEmail\", \"\");\r\n                }\r\n                setPaperlessStatus({\r\n                    ...paperlessStatus,\r\n                    [contractAccount]: {\r\n                        paperlessStatus: paperlessReq.data.result.isPaperLess,\r\n                        paperlessEmail: paperlessReq.data.result.userEmail\r\n                    }\r\n                });\r\n            }\r\n        }\r\n        const contractAccount = props.form.values.contractAccount;\r\n        if (contractAccount) {\r\n            if (paperlessStatus[contractAccount] === undefined) {\r\n                getPaperlessStatus();\r\n            } else if (typeof paperlessStatus[contractAccount].paperlessStatus === \"boolean\") {\r\n                props.form.setFieldValue(\"isPaperlessBilling\", paperlessStatus[contractAccount].paperlessStatus);\r\n                if (paperlessStatus[contractAccount].paperlessStatus) {\r\n                    props.form.setFieldValue(\"paperlessBillingEmail\", paperlessStatus[contractAccount].paperlessEmail);\r\n                } else {\r\n                    props.form.setFieldValue(\"paperlessBillingEmail\", \"\");\r\n                }\r\n            }\r\n        }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [\r\n        props.form.values.contractAccount\r\n    ]);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\r\n        const getCustomerDetails = async ()=>{\r\n            const isExistingBill = props.form.values.isExistingBill;\r\n            const contractAccountNumber = props.form.values.contractAccount;\r\n            let formatCustMaillingAdd = \"\";\r\n            if (isExistingBill === \"true\" && contractAccountNumber != \"\") {\r\n                var _req_data_result_mailingAddress, _req_data_result, _req_data, _req_data_result_mailingAddress1, _req_data_result1, _req_data1, _req_data_result_mailingAddress2, _req_data_result2, _req_data2, _req_data_result_mailingAddress3, _req_data_result3, _req_data3, _req_data_result_mailingAddress4, _req_data_result4, _req_data4, _req_data_result_mailingAddress5, _req_data_result5, _req_data5;\r\n                console.log(isExistingBill);\r\n                console.log(props.form.values.contractAccount);\r\n                const req = await axios_1_4__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"/api/myaccount/customerdetails\", {\r\n                    params: {\r\n                        ca: contractAccountNumber\r\n                    }\r\n                });\r\n                console.log(req.data);\r\n                formatCustMaillingAdd = ((_req_data = req.data) === null || _req_data === void 0 ? void 0 : (_req_data_result = _req_data.result) === null || _req_data_result === void 0 ? void 0 : (_req_data_result_mailingAddress = _req_data_result.mailingAddress) === null || _req_data_result_mailingAddress === void 0 ? void 0 : _req_data_result_mailingAddress.streetNumber) + \" \" + ((_req_data1 = req.data) === null || _req_data1 === void 0 ? void 0 : (_req_data_result1 = _req_data1.result) === null || _req_data_result1 === void 0 ? void 0 : (_req_data_result_mailingAddress1 = _req_data_result1.mailingAddress) === null || _req_data_result_mailingAddress1 === void 0 ? void 0 : _req_data_result_mailingAddress1.streetName) + \" \" + ((_req_data2 = req.data) === null || _req_data2 === void 0 ? void 0 : (_req_data_result2 = _req_data2.result) === null || _req_data_result2 === void 0 ? void 0 : (_req_data_result_mailingAddress2 = _req_data_result2.mailingAddress) === null || _req_data_result_mailingAddress2 === void 0 ? void 0 : _req_data_result_mailingAddress2.aptNumber) + \" \" + ((_req_data3 = req.data) === null || _req_data3 === void 0 ? void 0 : (_req_data_result3 = _req_data3.result) === null || _req_data_result3 === void 0 ? void 0 : (_req_data_result_mailingAddress3 = _req_data_result3.mailingAddress) === null || _req_data_result_mailingAddress3 === void 0 ? void 0 : _req_data_result_mailingAddress3.city) + \" \" + ((_req_data4 = req.data) === null || _req_data4 === void 0 ? void 0 : (_req_data_result4 = _req_data4.result) === null || _req_data_result4 === void 0 ? void 0 : (_req_data_result_mailingAddress4 = _req_data_result4.mailingAddress) === null || _req_data_result_mailingAddress4 === void 0 ? void 0 : _req_data_result_mailingAddress4.state) + \" \" + ((_req_data5 = req.data) === null || _req_data5 === void 0 ? void 0 : (_req_data_result5 = _req_data5.result) === null || _req_data_result5 === void 0 ? void 0 : (_req_data_result_mailingAddress5 = _req_data_result5.mailingAddress) === null || _req_data_result_mailingAddress5 === void 0 ? void 0 : _req_data_result_mailingAddress5.postalCode);\r\n                console.log(formatCustMaillingAdd);\r\n                setCustMaillingAddress(formatCustMaillingAdd);\r\n            }\r\n        };\r\n        getCustomerDetails();\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [\r\n        props.form.values.isExistingBill && props.form.values.contractAccount\r\n    ]);\r\n    function handleSameAddressChange(event) {\r\n        const isSameAddressChecked = event.currentTarget.checked;\r\n        setIsSameAddress(isSameAddressChecked);\r\n        if (isSameAddressChecked) {\r\n            props.form.setFieldValue(\"billingOption\", \"sameAddress\");\r\n        } else {\r\n            props.form.setFieldValue(\"billingOption\", \"differentAddress\");\r\n        }\r\n    }\r\n    function handlePoBoxChange(event) {\r\n        const isPoBoxChecked = event.currentTarget.checked;\r\n        setIsPoBox(isPoBoxChecked);\r\n        if (isPoBoxChecked) {\r\n            props.form.setFieldValue(\"billingOption\", \"poBox\");\r\n        } else {\r\n            props.form.setFieldValue(\"billingOption\", \"differentAddress\");\r\n        }\r\n    }\r\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n        children: [\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_8__.Text, {\r\n                tag: \"p\",\r\n                className: \"font-primaryBlack text-base sm:text-plus2 text-textQuattuordenary\",\r\n                field: {\r\n                    value: props.fields.BillingInformationTitle.value\r\n                }\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                lineNumber: 201,\r\n                columnNumber: 7\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"flex flex-col gap-6 mt-8\",\r\n                children: [\r\n                    props.form.values.isExistingBill === \"false\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                        children: [\r\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                className: \"flex gap-8 flex-col\",\r\n                                children: [\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Checkbox, {\r\n                                        radius: 0,\r\n                                        size: \"xs\",\r\n                                        label: props.fields.SameAsServiceAddressCheckText.value,\r\n                                        checked: isSameAddress,\r\n                                        onChange: handleSameAddressChange,\r\n                                        styles: ()=>{\r\n                                            if (!src_utils_util__WEBPACK_IMPORTED_MODULE_7__.isTxu) return {\r\n                                                label: {\r\n                                                    fontFamily: \"GothaPro-Bold\"\r\n                                                }\r\n                                            };\r\n                                            else return {};\r\n                                        }\r\n                                    }, void 0, false, {\r\n                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                        lineNumber: 210,\r\n                                        columnNumber: 15\r\n                                    }, undefined),\r\n                                    !isSameAddress ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Checkbox, {\r\n                                        radius: 0,\r\n                                        size: \"xs\",\r\n                                        checked: isPoBox,\r\n                                        onChange: handlePoBoxChange,\r\n                                        label: props.fields.POBoxCheckText.value,\r\n                                        styles: ()=>{\r\n                                            if (!src_utils_util__WEBPACK_IMPORTED_MODULE_7__.isTxu) return {\r\n                                                label: {\r\n                                                    fontFamily: \"GothaPro-Bold\"\r\n                                                }\r\n                                            };\r\n                                            else return {};\r\n                                        }\r\n                                    }, void 0, false, {\r\n                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                        lineNumber: 227,\r\n                                        columnNumber: 17\r\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\r\n                                ]\r\n                            }, void 0, true, {\r\n                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                lineNumber: 209,\r\n                                columnNumber: 13\r\n                            }, undefined),\r\n                            !isSameAddress && !isPoBox && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                className: \"flex flex-col gap-5 sm:gap-8 mt-2\",\r\n                                children: [\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex flex-col sm:flex-row gap-5 sm:gap-6\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.TextInput, {\r\n                                                label: props.fields.StreetNumberText.fields.Message.value,\r\n                                                ...props.form.getInputProps(\"billingStreetNumber\")\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                                lineNumber: 250,\r\n                                                columnNumber: 19\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.TextInput, {\r\n                                                label: props.fields.StreetAddressText.fields.Message.value,\r\n                                                ...props.form.getInputProps(\"billingStreetAddress\")\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                                lineNumber: 254,\r\n                                                columnNumber: 19\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.TextInput, {\r\n                                                label: props.fields.AptOrUnitText.fields.Message.value,\r\n                                                ...props.form.getInputProps(\"billingAptOrUnit\")\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                                lineNumber: 258,\r\n                                                columnNumber: 19\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                        lineNumber: 249,\r\n                                        columnNumber: 17\r\n                                    }, undefined),\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex flex-col gap-5 sm:flex-row sm:gap-6\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.TextInput, {\r\n                                                label: props.fields.CityText.fields.Message.value,\r\n                                                ...props.form.getInputProps(\"billingCity\")\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                                lineNumber: 264,\r\n                                                columnNumber: 19\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Select, {\r\n                                                styles: {\r\n                                                    wrapper: {\r\n                                                        [\"@media (max-width: 767px)\"]: {\r\n                                                            width: \"100%\"\r\n                                                        }\r\n                                                    },\r\n                                                    root: {\r\n                                                        [\"@media (max-width: 767px)\"]: {\r\n                                                            width: \"50%\"\r\n                                                        }\r\n                                                    }\r\n                                                },\r\n                                                data: props.fields.States.map((state)=>state.displayName),\r\n                                                label: \"State\",\r\n                                                ...props.form.getInputProps(\"billingState\"),\r\n                                                rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\r\n                                                    icon: _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_12__.faChevronDown,\r\n                                                    className: \"text-textSecondary hover:text-textPrimary\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                                    lineNumber: 285,\r\n                                                    columnNumber: 23\r\n                                                }, void 0),\r\n                                                selectOnBlur: true\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                                lineNumber: 268,\r\n                                                columnNumber: 19\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.TextInput, {\r\n                                                label: props.fields.ZipcodeText.fields.Message.value,\r\n                                                ...props.form.getInputProps(\"billingZipCode\"),\r\n                                                styles: {\r\n                                                    root: {\r\n                                                        [\"@media (max-width: 767px)\"]: {\r\n                                                            width: \"50%\"\r\n                                                        }\r\n                                                    }\r\n                                                }\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                                lineNumber: 292,\r\n                                                columnNumber: 19\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                        lineNumber: 263,\r\n                                        columnNumber: 17\r\n                                    }, undefined)\r\n                                ]\r\n                            }, void 0, true, {\r\n                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                lineNumber: 248,\r\n                                columnNumber: 15\r\n                            }, undefined),\r\n                            !isSameAddress && isPoBox && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                className: \"flex flex-col gap-8\",\r\n                                children: [\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex flex-row gap-6\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.TextInput, {\r\n                                                label: props.fields.POBoxText.fields.Message.value,\r\n                                                ...props.form.getInputProps(\"poBox\")\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                                lineNumber: 309,\r\n                                                columnNumber: 19\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.TextInput, {\r\n                                                label: props.fields.CityText.fields.Message.value,\r\n                                                ...props.form.getInputProps(\"poBoxCity\")\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                                lineNumber: 313,\r\n                                                columnNumber: 19\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                        lineNumber: 308,\r\n                                        columnNumber: 17\r\n                                    }, undefined),\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex flex-row gap-6\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Select, {\r\n                                                styles: {\r\n                                                    wrapper: {\r\n                                                        width: \"156px\"\r\n                                                    }\r\n                                                },\r\n                                                data: props.fields.States.map((state)=>state.displayName),\r\n                                                label: \"State\",\r\n                                                ...props.form.getInputProps(\"poBoxState\"),\r\n                                                rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\r\n                                                    icon: _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_12__.faChevronDown,\r\n                                                    className: \"text-textSecondary hover:text-textPrimary\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                                    lineNumber: 329,\r\n                                                    columnNumber: 23\r\n                                                }, void 0),\r\n                                                selectOnBlur: true\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                                lineNumber: 319,\r\n                                                columnNumber: 19\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.TextInput, {\r\n                                                label: props.fields.ZipcodeText.fields.Message.value,\r\n                                                ...props.form.getInputProps(\"poBoxZipCode\")\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                                lineNumber: 336,\r\n                                                columnNumber: 19\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                        lineNumber: 318,\r\n                                        columnNumber: 17\r\n                                    }, undefined)\r\n                                ]\r\n                            }, void 0, true, {\r\n                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                lineNumber: 307,\r\n                                columnNumber: 15\r\n                            }, undefined)\r\n                        ]\r\n                    }, void 0, true, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                        lineNumber: 208,\r\n                        columnNumber: 11\r\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                        className: \"text-sm\",\r\n                        children: [\r\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\r\n                                className: \"font-primaryBold \",\r\n                                children: \"Mailling Address: \"\r\n                            }, void 0, false, {\r\n                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                lineNumber: 346,\r\n                                columnNumber: 13\r\n                            }, undefined),\r\n                            custMaillingAddress\r\n                        ]\r\n                    }, void 0, true, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                        lineNumber: 345,\r\n                        columnNumber: 11\r\n                    }, undefined),\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                        className: \"flex flex-row gap-1\",\r\n                        children: [\r\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Checkbox, {\r\n                                styles: {\r\n                                    labelWrapper: {\r\n                                        [\"@media (max-width: 640px)\"]: {\r\n                                            width: \"270px\"\r\n                                        }\r\n                                    }\r\n                                },\r\n                                checked: isSecondaryAccountHolder,\r\n                                ...props.form.getInputProps(\"isSecondaryAccountHolder\"),\r\n                                label: props.fields.SecondaryAccountHolderCheckText.value,\r\n                                radius: \"xs\",\r\n                                size: \"xs\"\r\n                            }, void 0, false, {\r\n                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                lineNumber: 351,\r\n                                columnNumber: 11\r\n                            }, undefined),\r\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_Elements_Tooltip_Tooltip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\r\n                                content: {\r\n                                    value: props.fields.SecondaryAccountHolderTooltipText.value\r\n                                },\r\n                                className: \"billing-tooltip\",\r\n                                arrowclassName: \"billing-tooltip-icon\",\r\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(assets_icons_QuestionCircle__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\r\n                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                    lineNumber: 372,\r\n                                    columnNumber: 13\r\n                                }, undefined)\r\n                            }, void 0, false, {\r\n                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                lineNumber: 365,\r\n                                columnNumber: 11\r\n                            }, undefined)\r\n                        ]\r\n                    }, void 0, true, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                        lineNumber: 350,\r\n                        columnNumber: 9\r\n                    }, undefined),\r\n                    isSecondaryAccountHolder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                        className: \"flex flex-col sm:flex-row gap-4 sm:gap-6\",\r\n                        children: [\r\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.TextInput, {\r\n                                label: props.fields.SAHFirstNameText.value,\r\n                                ...props.form.getInputProps(\"secondaryAccountFirstName\")\r\n                            }, void 0, false, {\r\n                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                lineNumber: 377,\r\n                                columnNumber: 13\r\n                            }, undefined),\r\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.TextInput, {\r\n                                label: props.fields.SAHLastNameText.value,\r\n                                ...props.form.getInputProps(\"secondaryAccountLastName\")\r\n                            }, void 0, false, {\r\n                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                lineNumber: 381,\r\n                                columnNumber: 13\r\n                            }, undefined)\r\n                        ]\r\n                    }, void 0, true, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                        lineNumber: 376,\r\n                        columnNumber: 11\r\n                    }, undefined),\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Checkbox, {\r\n                        ...props.form.getInputProps(\"isPaperlessBilling\"),\r\n                        checked: isPaperlessBilling,\r\n                        radius: 0,\r\n                        size: \"xs\",\r\n                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            children: [\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_8__.RichText, {\r\n                                    tag: \"p\",\r\n                                    className: \"inline-link font-primaryRegular leading-[25px] mb-[5px]\",\r\n                                    field: {\r\n                                        value: props.fields.PaperlessBillingCheckText.value\r\n                                    }\r\n                                }, void 0, false, {\r\n                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                    lineNumber: 394,\r\n                                    columnNumber: 15\r\n                                }, void 0),\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.UnstyledButton, {\r\n                                    component: \"a\",\r\n                                    className: \"text-textSecondary hover:text-textPrimary underline decoration-solid  decoration-2 decoration-textPrimary decoration-offset-4 font-primaryRegular\",\r\n                                    href: props.fields.TermsAndCondition.value.href,\r\n                                    target: \"_blank\",\r\n                                    children: props.fields.TermsAndCondition.value.text\r\n                                }, void 0, false, {\r\n                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                                    lineNumber: 402,\r\n                                    columnNumber: 15\r\n                                }, void 0)\r\n                            ]\r\n                        }, void 0, true, {\r\n                            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                            lineNumber: 393,\r\n                            columnNumber: 13\r\n                        }, void 0)\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                        lineNumber: 387,\r\n                        columnNumber: 9\r\n                    }, undefined),\r\n                    isPaperlessBilling ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.TextInput, {\r\n                        styles: {\r\n                            root: {\r\n                                width: \"280px\",\r\n                                [\"@media (max-width: 640px)\"]: {\r\n                                    width: \"100%\"\r\n                                }\r\n                            }\r\n                        },\r\n                        ...props.form.getInputProps(\"paperlessBillingEmail\"),\r\n                        label: props.fields.EmailForPaperlessBillingText.value\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                        lineNumber: 414,\r\n                        columnNumber: 11\r\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\r\n                    isEV && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Checkbox, {\r\n                        radius: 0,\r\n                        size: \"xs\",\r\n                        checked: isEVDisclaimerSelected,\r\n                        onChange: ()=>setIsEVDisclaimerSelected(!isEVDisclaimerSelected),\r\n                        label: props.fields.EVDisclaimer.fields.EVCheckbox.value\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                        lineNumber: 430,\r\n                        columnNumber: 11\r\n                    }, undefined)\r\n                ]\r\n            }, void 0, true, {\r\n                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n                lineNumber: 206,\r\n                columnNumber: 7\r\n            }, undefined)\r\n        ]\r\n    }, void 0, true, {\r\n        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\MyAccount\\\\Add\\\\AddBillingInfo\\\\AddBillingInfo.tsx\",\r\n        lineNumber: 200,\r\n        columnNumber: 5\r\n    }, undefined);\r\n};\r\n_s(AddBillingInfo, \"dg0CP8G0HMslzMNPrvX9C1BOKvg=\", false, function() {\r\n    return [\r\n        _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_8__.useSitecoreContext\r\n    ];\r\n});\r\n_c = AddBillingInfo;\r\n\r\n// const Component = withDatasourceCheck()<AddBillingInfoProps>(AddBillingInfo);\r\n// export default aiLogger(Component, Component.name);\r\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddBillingInfo);\r\nvar _c;\r\n$RefreshReg$(_c, \"AddBillingInfo\");\r\n\r\n\r\n;\r\n    // Wrapped in an IIFE to avoid polluting the global scope\r\n    ;\r\n    (function () {\r\n        var _a, _b;\r\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\r\n        // to extract CSS. For backwards compatibility, we need to check we're in a\r\n        // browser context before continuing.\r\n        if (typeof self !== 'undefined' &&\r\n            // AMP / No-JS mode does not inject these helpers:\r\n            '$RefreshHelpers$' in self) {\r\n            // @ts-ignore __webpack_module__ is global\r\n            var currentExports = module.exports;\r\n            // @ts-ignore __webpack_module__ is global\r\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\r\n            // This cannot happen in MainTemplate because the exports mismatch between\r\n            // templating and execution.\r\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\r\n            // A module can be accepted automatically based on its exports, e.g. when\r\n            // it is a Refresh Boundary.\r\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\r\n                // Save the previous exports signature on update so we can compare the boundary\r\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\r\n                module.hot.dispose(function (data) {\r\n                    data.prevSignature =\r\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\r\n                });\r\n                // Unconditionally accept an update to this module, we'll check if it's\r\n                // still a Refresh Boundary later.\r\n                // @ts-ignore importMeta is replaced in the loader\r\n                module.hot.accept();\r\n                // This field is set when the previous version of this module was a\r\n                // Refresh Boundary, letting us know we need to check for invalidation or\r\n                // enqueue an update.\r\n                if (prevSignature !== null) {\r\n                    // A boundary can become ineligible if its exports are incompatible\r\n                    // with the previous exports.\r\n                    //\r\n                    // For example, if you add/remove/change exports, we'll want to\r\n                    // re-execute the importing modules, and force those components to\r\n                    // re-render. Similarly, if you convert a class component to a\r\n                    // function, we want to invalidate the boundary.\r\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\r\n                        module.hot.invalidate();\r\n                    }\r\n                    else {\r\n                        self.$RefreshHelpers$.scheduleUpdate();\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                // Since we just executed the code for the module, it's possible that the\r\n                // new exports made it ineligible for being a boundary.\r\n                // We only care about the case when we were _previously_ a boundary,\r\n                // because we already accepted this update (accidental side effect).\r\n                var isNoLongerABoundary = prevSignature !== null;\r\n                if (isNoLongerABoundary) {\r\n                    module.hot.invalidate();\r\n                }\r\n            }\r\n        }\r\n    })();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/MyAccount/Add/AddBillingInfo/AddBillingInfo.tsx\n"));

/***/ })

});