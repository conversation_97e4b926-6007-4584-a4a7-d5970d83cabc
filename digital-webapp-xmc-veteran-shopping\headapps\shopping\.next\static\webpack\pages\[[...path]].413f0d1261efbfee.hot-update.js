"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[[...path]]",{

/***/ "./src/components/common/SelectedPlanCardWithDetails/SelectedPlanCardWithDetails.tsx":
/*!*******************************************************************************************!*\
  !*** ./src/components/common/SelectedPlanCardWithDetails/SelectedPlanCardWithDetails.tsx ***!
  \*******************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SelectedPlanCardWithDetails: function() { return /* binding */ SelectedPlanCardWithDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_pro_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fortawesome/pro-solid-svg-icons */ \"./node_modules/@fortawesome/pro-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss-nextjs */ \"./node_modules/@sitecore-jss/sitecore-jss-nextjs/dist/esm/index.js\");\n/* harmony import */ var src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/hoc/ApplicationInsightsLogger */ \"./src/hoc/ApplicationInsightsLogger.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var assets_icons_DownloadIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! assets/icons/DownloadIcon */ \"./src/assets/icons/DownloadIcon.tsx\");\n/* harmony import */ var src_stores_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/stores/store */ \"./src/stores/store.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var src_utils_cn__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/utils/cn */ \"./src/utils/cn.ts\");\n/* harmony import */ var src_utils_util__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! src/utils/util */ \"./src/utils/util.ts\");\n/* harmony import */ var next_localization__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-localization */ \"./node_modules/next-localization/dist/index.modern.js\");\n/* harmony import */ var axios_1_4__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! axios-1.4 */ \"./node_modules/axios-1.4/index.js\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"./node_modules/@mantine/core/esm/index.js\");\n/* harmony import */ var src_utils_getIncentives__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! src/utils/getIncentives */ \"./src/utils/getIncentives.ts\");\n\r\nvar _s = $RefreshSig$();\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst SelectedPlanCardWithDetails = (props)=>{\r\n    var _props_rendering, _selectedPlan_trieProductRateDetails;\r\n    _s();\r\n    console.log(props);\r\n    const { locale } = (0,next_localization__WEBPACK_IMPORTED_MODULE_9__.useI18n)();\r\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\r\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\r\n    const { cint, prom, dwel, zip, tdsp } = router.query;\r\n    const data = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.useComponentProps)((_props_rendering = props.rendering) === null || _props_rendering === void 0 ? void 0 : _props_rendering.uid);\r\n    const access_token = data === null || data === void 0 ? void 0 : data.access_token;\r\n    const sessionId = data === null || data === void 0 ? void 0 : data.sessionid;\r\n    const selectedPlan = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_5__.useAppSelector)((state)=>state.plans.selectedPlan);\r\n    const [selectedPlanDetails, setSelectedPlanDetails] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\r\n    const energyCharge = selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan_trieProductRateDetails = selectedPlan.trieProductRateDetails) === null || _selectedPlan_trieProductRateDetails === void 0 ? void 0 : _selectedPlan_trieProductRateDetails.find((item)=>item.conditionType === \"Z100\" || item.conditionType === \"ZESC\" || item.conditionType === \"Z250\");\r\n    const renderdigitalRate = (val)=>{\r\n        var _selectedPlan_ePlanRates, _this;\r\n        const digitalRecord = selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan_ePlanRates = selectedPlan.ePlanRates) === null || _selectedPlan_ePlanRates === void 0 ? void 0 : _selectedPlan_ePlanRates.find((item)=>{\r\n            var _item_description;\r\n            return (item === null || item === void 0 ? void 0 : (_item_description = item.description) === null || _item_description === void 0 ? void 0 : _item_description.toString()) === (val === null || val === void 0 ? void 0 : val.toString());\r\n        });\r\n        return (digitalRecord === null || digitalRecord === void 0 ? void 0 : digitalRecord.value) != undefined ? (_this = (digitalRecord === null || digitalRecord === void 0 ? void 0 : digitalRecord.value) * 100) === null || _this === void 0 ? void 0 : _this.toFixed(1) : \"\";\r\n    };\r\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\r\n        const fetchData = async ()=>{\r\n            var _plans_data_plans_result_offers, _plans_data_plans_result, _plans_data_plans, _plans_data;\r\n            const plans = await axios_1_4__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"/api/offers/fetchplans\", {\r\n                params: {\r\n                    cint: cint,\r\n                    locale: data === null || data === void 0 ? void 0 : data.locale,\r\n                    dwel: dwel,\r\n                    zip: zip,\r\n                    tdsp: tdsp,\r\n                    sessionId: sessionId,\r\n                    prom: prom\r\n                }\r\n            });\r\n            const enrolledPlan = plans === null || plans === void 0 ? void 0 : (_plans_data = plans.data) === null || _plans_data === void 0 ? void 0 : (_plans_data_plans = _plans_data.plans) === null || _plans_data_plans === void 0 ? void 0 : (_plans_data_plans_result = _plans_data_plans.result) === null || _plans_data_plans_result === void 0 ? void 0 : (_plans_data_plans_result_offers = _plans_data_plans_result.offers) === null || _plans_data_plans_result_offers === void 0 ? void 0 : _plans_data_plans_result_offers.filter((plan)=>plan.id === selectedPlan.planId);\r\n            if (enrolledPlan && enrolledPlan.length > 0) {\r\n                var _enrolledPlan_, _enrolledPlan_1, _enrolledPlan_2, _enrolledPlan_3, _enrolledPlan_4;\r\n                const planIncentives = (0,src_utils_getIncentives__WEBPACK_IMPORTED_MODULE_10__.getPlanIncentives)((_enrolledPlan_ = enrolledPlan[0]) === null || _enrolledPlan_ === void 0 ? void 0 : _enrolledPlan_.incentiveId, props.fields.IncentivesList);\r\n                const details = {\r\n                    planBenefits: (_enrolledPlan_1 = enrolledPlan[0]) === null || _enrolledPlan_1 === void 0 ? void 0 : _enrolledPlan_1.planBenefits,\r\n                    planDisclaimer: (_enrolledPlan_2 = enrolledPlan[0]) === null || _enrolledPlan_2 === void 0 ? void 0 : _enrolledPlan_2.planDisclaimer,\r\n                    incentiveId: (_enrolledPlan_3 = enrolledPlan[0]) === null || _enrolledPlan_3 === void 0 ? void 0 : _enrolledPlan_3.incentiveId,\r\n                    incentiveDisclaimer: planIncentives.length > 0 ? planIncentives[1] : \"\",\r\n                    incentiveSpecialOfferText: planIncentives.length > 0 ? planIncentives[0] : \"\",\r\n                    oneLineSummary: (_enrolledPlan_4 = enrolledPlan[0]) === null || _enrolledPlan_4 === void 0 ? void 0 : _enrolledPlan_4.oneLineSummary\r\n                };\r\n                setSelectedPlanDetails(details);\r\n            }\r\n        };\r\n        if (access_token !== \"\") {\r\n            fetchData();\r\n        }\r\n    }, [\r\n        selectedPlan,\r\n        locale()\r\n    ]);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\r\n        setSelectedPlanDetails(undefined);\r\n    }, [\r\n        locale()\r\n    ]);\r\n    if (selectedPlanDetails === undefined) {\r\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n            className: \"w-full h-[250px] flex justify-center\",\r\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Loader, {\r\n                size: \"lg\"\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                lineNumber: 149,\r\n                columnNumber: 9\r\n            }, undefined)\r\n        }, void 0, false, {\r\n            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n            lineNumber: 148,\r\n            columnNumber: 7\r\n        }, undefined);\r\n    } else {\r\n        var _props_fields_MonthsLabelText, _props_fields, _props_fields1, _props_fields2, _selectedPlanDetails_planBenefits, _selectedPlanDetails_planDisclaimer, _selectedPlanDetails_incentiveDisclaimer, _props_fields_ElectricityFactsLabelText, _props_fields3, _props_fields_TermsOfServiceText, _props_fields4, _props_fields_YourRightsAsaCustomerText, _props_fields5;\r\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n            children: [\r\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                    tag: \"p\",\r\n                    className: \"font-primaryBold text-textQuattuordenary text-[24px] leading-[30px]\",\r\n                    field: props.fields.Heading\r\n                }, void 0, false, {\r\n                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                    lineNumber: 155,\r\n                    columnNumber: 9\r\n                }, undefined),\r\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                    className: \"flex flex-row sm:flex-row mt-[22px] rounded-[4px]\",\r\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                        className: (0,src_utils_cn__WEBPACK_IMPORTED_MODULE_7__.cn)(\"border-borderSeptendenary bg-bgNovemdenary w-full shadow-3xl rounded-b-xl rounded-xl\", {\r\n                            \"print:w-full\": src_utils_util__WEBPACK_IMPORTED_MODULE_8__.isAMB\r\n                        }),\r\n                        children: [\r\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                className: \"w-full h-12 bg-bgVigintiunary flex justify-center items-center rounded-t-xl wide:m-auto ipad:m-auto ipad:max-w-full\"\r\n                            }, void 0, false, {\r\n                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                lineNumber: 169,\r\n                                columnNumber: 13\r\n                            }, undefined),\r\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                className: \"flex flex-col sm:flex-row p-6 sm:w-[800px] w-full\",\r\n                                children: [\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"w-full sm:w-1/2\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"flex flex-col sm:flex-col items-start sm:items-center\",\r\n                                                children: [\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\r\n                                                        className: \"border-solid border-borderSeptendenary border-[1px] w-full my-6 sm:hidden order-2 sm:order-none print:hidden\"\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                        lineNumber: 173,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                        className: \"order-3 sm:order-none\",\r\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                            tag: \"p\",\r\n                                                            className: \"font-primaryBold text-textQuattuordenary text-plus2 sm:text-plus2\",\r\n                                                            field: {\r\n                                                                value: selectedPlan.planName\r\n                                                            }\r\n                                                        }, void 0, false, {\r\n                                                            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                            lineNumber: 175,\r\n                                                            columnNumber: 21\r\n                                                        }, undefined)\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                        lineNumber: 174,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                        className: \"flex flex-col sm:flex-row items-center mt-2 sm:mt-0 order-1 sm:order-none\",\r\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                            className: \"sm:ml-2 flex flex-col gap-1 items-center\",\r\n                                                            children: [\r\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                                    tag: \"p\",\r\n                                                                    className: \"font-primaryBold text-textQuattuordenary text-plus4\",\r\n                                                                    field: {\r\n                                                                        value: (selectedPlan.rate * 100).toFixed(1) + \"\\xa2\"\r\n                                                                    }\r\n                                                                }, void 0, false, {\r\n                                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                    lineNumber: 184,\r\n                                                                    columnNumber: 23\r\n                                                                }, undefined),\r\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                                    tag: \"p\",\r\n                                                                    className: \"text-textQuattuordenary text-minus3 sm:text-minus2\",\r\n                                                                    field: dwel === \"02\" ? props.fields.PerkWhTextApt : props.fields.PerkWhText\r\n                                                                }, void 0, false, {\r\n                                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                    lineNumber: 189,\r\n                                                                    columnNumber: 23\r\n                                                                }, undefined)\r\n                                                            ]\r\n                                                        }, void 0, true, {\r\n                                                            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                            lineNumber: 183,\r\n                                                            columnNumber: 21\r\n                                                        }, undefined)\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                        lineNumber: 182,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                        className: \"flex-[2] flex flex-col gap-1\",\r\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                            className: \"flex flex-row\",\r\n                                                            children: [\r\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                                    tag: \"p\",\r\n                                                                    className: \"font-primaryBold text-textQuattuordenary text-minus2 ml-2\",\r\n                                                                    field: {\r\n                                                                        value: selectedPlan.term + \" \" + ((_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_MonthsLabelText = _props_fields.MonthsLabelText) === null || _props_fields_MonthsLabelText === void 0 ? void 0 : _props_fields_MonthsLabelText.value)\r\n                                                                    }\r\n                                                                }, void 0, false, {\r\n                                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                    lineNumber: 198,\r\n                                                                    columnNumber: 23\r\n                                                                }, undefined),\r\n                                                                \"/\",\r\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                                    tag: \"p\",\r\n                                                                    className: \"font-primaryBold text-textQuattuordenary text-minus2\",\r\n                                                                    field: {\r\n                                                                        value: selectedPlan.rateType\r\n                                                                    }\r\n                                                                }, void 0, false, {\r\n                                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                    lineNumber: 206,\r\n                                                                    columnNumber: 23\r\n                                                                }, undefined)\r\n                                                            ]\r\n                                                        }, void 0, true, {\r\n                                                            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                            lineNumber: 197,\r\n                                                            columnNumber: 21\r\n                                                        }, undefined)\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                        lineNumber: 196,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                        className: \"flex flex-row my-10 sm:my-10 text-textPrimary hover:text-hoverSecondary\",\r\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\r\n                                                            onClick: ()=>setShowDetails((val)=>!val),\r\n                                                            className: \"font-primaryBold text-minus3 sm:text-[18px] cursor-pointer flex flex-row justify-between sm:justify-normal items-center w-full \",\r\n                                                            children: [\r\n                                                                showDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                                    field: (_props_fields1 = props.fields) === null || _props_fields1 === void 0 ? void 0 : _props_fields1.HideDetailsText\r\n                                                                }, void 0, false, {\r\n                                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                    lineNumber: 220,\r\n                                                                    columnNumber: 25\r\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                                    field: (_props_fields2 = props.fields) === null || _props_fields2 === void 0 ? void 0 : _props_fields2.ShowDetailsText\r\n                                                                }, void 0, false, {\r\n                                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                    lineNumber: 222,\r\n                                                                    columnNumber: 25\r\n                                                                }, undefined),\r\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\r\n                                                                    className: \"sm:pl-1 pl-1\",\r\n                                                                    icon: showDetails ? _fortawesome_pro_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faChevronUp : _fortawesome_pro_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faChevronDown\r\n                                                                }, void 0, false, {\r\n                                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                    lineNumber: 225,\r\n                                                                    columnNumber: 23\r\n                                                                }, undefined)\r\n                                                            ]\r\n                                                        }, void 0, true, {\r\n                                                            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                            lineNumber: 215,\r\n                                                            columnNumber: 21\r\n                                                        }, undefined)\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                        lineNumber: 214,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                        className: \"flex flex-col sm:flex-row mt-2\",\r\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                            className: \"flex-[4] pr-10\",\r\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                                tag: \"p\",\r\n                                                                className: \"font-primaryRegular text-textQuattuordenary text-minus3 leading-[20px] sm:text-minus2\",\r\n                                                                field: {\r\n                                                                    value: selectedPlanDetails === null || selectedPlanDetails === void 0 ? void 0 : selectedPlanDetails.oneLineSummary\r\n                                                                }\r\n                                                            }, void 0, false, {\r\n                                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                lineNumber: 235,\r\n                                                                columnNumber: 25\r\n                                                            }, undefined)\r\n                                                        }, void 0, false, {\r\n                                                            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                            lineNumber: 234,\r\n                                                            columnNumber: 23\r\n                                                        }, undefined)\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                        lineNumber: 233,\r\n                                                        columnNumber: 21\r\n                                                    }, undefined)\r\n                                                ]\r\n                                            }, void 0, true, {\r\n                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                lineNumber: 172,\r\n                                                columnNumber: 17\r\n                                            }, undefined),\r\n                                            selectedPlanDetails.incentiveSpecialOfferText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\r\n                                                className: \"flex flex-row gap-2 items-center py-4\",\r\n                                                children: [\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\r\n                                                        icon: _fortawesome_pro_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faGift,\r\n                                                        size: \"sm\",\r\n                                                        color: \"#9E1E62\"\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                        lineNumber: 247,\r\n                                                        columnNumber: 21\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\r\n                                                        className: \"flex-none font-primaryBold\",\r\n                                                        children: selectedPlanDetails.incentiveSpecialOfferText\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                        lineNumber: 248,\r\n                                                        columnNumber: 21\r\n                                                    }, undefined)\r\n                                                ]\r\n                                            }, void 0, true, {\r\n                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                lineNumber: 246,\r\n                                                columnNumber: 19\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"flex sm:hidden print:hidden\",\r\n                                                children: [\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                        tag: \"p\",\r\n                                                        className: \"font-primaryBold text-textQuattuordenary text-minus3 sm:text-minus1 print:hidden\",\r\n                                                        field: props.fields.NeedHelpText\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                        lineNumber: 254,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                        tag: \"p\",\r\n                                                        className: \"font-primaryBold text-textPrimary hover:text-textSecondary text-minus3 sm:text-minus1 ml-[8px]\",\r\n                                                        field: props.fields.NeedHelpPhNumber\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                        lineNumber: 259,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined)\r\n                                                ]\r\n                                            }, void 0, true, {\r\n                                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                lineNumber: 253,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                        lineNumber: 171,\r\n                                        columnNumber: 15\r\n                                    }, undefined),\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"w-full sm:w-1/2\",\r\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                            className: (0,src_utils_cn__WEBPACK_IMPORTED_MODULE_7__.cn)(\"flex gap-2 sm:gap-[105px] flex-col sm:flex-col sm:justify-start px-4 pt-0\", {\r\n                                                \"print:bg-white\": src_utils_util__WEBPACK_IMPORTED_MODULE_8__.isAMB\r\n                                            }),\r\n                                            children: [\r\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                    className: \"flex flex-col gap-4 w-full sm:w-80 text-textQuattuordenary\",\r\n                                                    children: [\r\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.RichText, {\r\n                                                            tag: \"span\",\r\n                                                            className: \"text-[14px] font-primaryRegular list-disc-custom breakSeparator\",\r\n                                                            field: {\r\n                                                                value: selectedPlanDetails === null || selectedPlanDetails === void 0 ? void 0 : (_selectedPlanDetails_planBenefits = selectedPlanDetails.planBenefits) === null || _selectedPlanDetails_planBenefits === void 0 ? void 0 : _selectedPlanDetails_planBenefits.toString()\r\n                                                            }\r\n                                                        }, void 0, false, {\r\n                                                            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                            lineNumber: 275,\r\n                                                            columnNumber: 21\r\n                                                        }, undefined),\r\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.RichText, {\r\n                                                            tag: \"span\",\r\n                                                            className: \"text-sm font-primaryRegular\",\r\n                                                            field: {\r\n                                                                value: selectedPlanDetails === null || selectedPlanDetails === void 0 ? void 0 : (_selectedPlanDetails_planDisclaimer = selectedPlanDetails.planDisclaimer) === null || _selectedPlanDetails_planDisclaimer === void 0 ? void 0 : _selectedPlanDetails_planDisclaimer.toString()\r\n                                                            }\r\n                                                        }, void 0, false, {\r\n                                                            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                            lineNumber: 280,\r\n                                                            columnNumber: 21\r\n                                                        }, undefined),\r\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.RichText, {\r\n                                                            tag: \"span\",\r\n                                                            className: \"text-sm font-primaryRegular\",\r\n                                                            field: {\r\n                                                                value: selectedPlanDetails === null || selectedPlanDetails === void 0 ? void 0 : (_selectedPlanDetails_incentiveDisclaimer = selectedPlanDetails.incentiveDisclaimer) === null || _selectedPlanDetails_incentiveDisclaimer === void 0 ? void 0 : _selectedPlanDetails_incentiveDisclaimer.toString()\r\n                                                            }\r\n                                                        }, void 0, false, {\r\n                                                            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                            lineNumber: 285,\r\n                                                            columnNumber: 21\r\n                                                        }, undefined)\r\n                                                    ]\r\n                                                }, void 0, true, {\r\n                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                    lineNumber: 274,\r\n                                                    columnNumber: 19\r\n                                                }, undefined),\r\n                                                showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                    className: \"flex flex-col gap-4 custom-download\",\r\n                                                    children: [\r\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Link, {\r\n                                                            target: \"_blank\",\r\n                                                            field: {\r\n                                                                value: {\r\n                                                                    href: selectedPlan.EFLUrl\r\n                                                                }\r\n                                                            },\r\n                                                            className: \"text-textPrimary  hover:text-textSecondary flex flex-row text-minus1 font-primaryBold items-center\",\r\n                                                            children: [\r\n                                                                (_props_fields3 = props.fields) === null || _props_fields3 === void 0 ? void 0 : (_props_fields_ElectricityFactsLabelText = _props_fields3.ElectricityFactsLabelText) === null || _props_fields_ElectricityFactsLabelText === void 0 ? void 0 : _props_fields_ElectricityFactsLabelText.value,\r\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\r\n                                                                    className: \"pl-2\",\r\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(assets_icons_DownloadIcon__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\r\n                                                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                        lineNumber: 305,\r\n                                                                        columnNumber: 27\r\n                                                                    }, undefined)\r\n                                                                }, void 0, false, {\r\n                                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                    lineNumber: 304,\r\n                                                                    columnNumber: 25\r\n                                                                }, undefined)\r\n                                                            ]\r\n                                                        }, void 0, true, {\r\n                                                            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                            lineNumber: 294,\r\n                                                            columnNumber: 23\r\n                                                        }, undefined),\r\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Link, {\r\n                                                            target: \"_blank\",\r\n                                                            field: {\r\n                                                                value: {\r\n                                                                    href: selectedPlan.TOSUrl\r\n                                                                }\r\n                                                            },\r\n                                                            className: \"text-textPrimary hover:text-textSecondary flex flex-row text-minus1 font-primaryBold items-center\",\r\n                                                            children: [\r\n                                                                (_props_fields4 = props.fields) === null || _props_fields4 === void 0 ? void 0 : (_props_fields_TermsOfServiceText = _props_fields4.TermsOfServiceText) === null || _props_fields_TermsOfServiceText === void 0 ? void 0 : _props_fields_TermsOfServiceText.value,\r\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\r\n                                                                    className: \"pl-2\",\r\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(assets_icons_DownloadIcon__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\r\n                                                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                        lineNumber: 317,\r\n                                                                        columnNumber: 27\r\n                                                                    }, undefined)\r\n                                                                }, void 0, false, {\r\n                                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                    lineNumber: 316,\r\n                                                                    columnNumber: 25\r\n                                                                }, undefined)\r\n                                                            ]\r\n                                                        }, void 0, true, {\r\n                                                            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                            lineNumber: 308,\r\n                                                            columnNumber: 23\r\n                                                        }, undefined),\r\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Link, {\r\n                                                            target: \"_blank\",\r\n                                                            field: {\r\n                                                                value: {\r\n                                                                    href: selectedPlan.YRCUrl\r\n                                                                }\r\n                                                            },\r\n                                                            className: \"text-textPrimary hover:text-textSecondary flex flex-row text-minus1 font-primaryBold items-center\",\r\n                                                            children: [\r\n                                                                (_props_fields5 = props.fields) === null || _props_fields5 === void 0 ? void 0 : (_props_fields_YourRightsAsaCustomerText = _props_fields5.YourRightsAsaCustomerText) === null || _props_fields_YourRightsAsaCustomerText === void 0 ? void 0 : _props_fields_YourRightsAsaCustomerText.value,\r\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\r\n                                                                    className: \"pl-2\",\r\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(assets_icons_DownloadIcon__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\r\n                                                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                        lineNumber: 329,\r\n                                                                        columnNumber: 27\r\n                                                                    }, undefined)\r\n                                                                }, void 0, false, {\r\n                                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                                    lineNumber: 328,\r\n                                                                    columnNumber: 25\r\n                                                                }, undefined)\r\n                                                            ]\r\n                                                        }, void 0, true, {\r\n                                                            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                            lineNumber: 320,\r\n                                                            columnNumber: 23\r\n                                                        }, undefined)\r\n                                                    ]\r\n                                                }, void 0, true, {\r\n                                                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                                    lineNumber: 293,\r\n                                                    columnNumber: 21\r\n                                                }, undefined)\r\n                                            ]\r\n                                        }, void 0, true, {\r\n                                            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                            lineNumber: 268,\r\n                                            columnNumber: 17\r\n                                        }, undefined)\r\n                                    }, void 0, false, {\r\n                                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                        lineNumber: 267,\r\n                                        columnNumber: 15\r\n                                    }, undefined)\r\n                                ]\r\n                            }, void 0, true, {\r\n                                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                                lineNumber: 170,\r\n                                columnNumber: 13\r\n                            }, undefined)\r\n                        ]\r\n                    }, void 0, true, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                        lineNumber: 161,\r\n                        columnNumber: 11\r\n                    }, undefined)\r\n                }, void 0, false, {\r\n                    fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n                    lineNumber: 160,\r\n                    columnNumber: 9\r\n                }, undefined)\r\n            ]\r\n        }, void 0, true, {\r\n            fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\common\\\\SelectedPlanCardWithDetails\\\\SelectedPlanCardWithDetails.tsx\",\r\n            lineNumber: 154,\r\n            columnNumber: 7\r\n        }, undefined);\r\n    }\r\n};\r\n_s(SelectedPlanCardWithDetails, \"P9ru+ucMpqjT6PMp6fYnohiNSyg=\", false, function() {\r\n    return [\r\n        next_localization__WEBPACK_IMPORTED_MODULE_9__.useI18n,\r\n        next_router__WEBPACK_IMPORTED_MODULE_6__.useRouter,\r\n        _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.useComponentProps,\r\n        src_stores_store__WEBPACK_IMPORTED_MODULE_5__.useAppSelector\r\n    ];\r\n});\r\n_c = SelectedPlanCardWithDetails;\r\n\r\nconst Component = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.withDatasourceCheck)()(SelectedPlanCardWithDetails);\r\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(Component, Component.name));\r\nvar _c, _c1;\r\n$RefreshReg$(_c, \"SelectedPlanCardWithDetails\");\r\n$RefreshReg$(_c1, \"%default%\");\r\n\r\n\r\n// Wrapped in an IIFE to avoid polluting the global scope\r\n;\r\n(function () {\r\n    var _a, _b;\r\n    // Legacy CSS implementations will `eval` browser code in a Node.js context\r\n    // to extract CSS. For backwards compatibility, we need to check we're in a\r\n    // browser context before continuing.\r\n    if (typeof self !== 'undefined' &&\r\n        // AMP / No-JS mode does not inject these helpers:\r\n        '$RefreshHelpers$' in self) {\r\n        // @ts-ignore __webpack_module__ is global\r\n        var currentExports = module.exports;\r\n        // @ts-ignore __webpack_module__ is global\r\n        var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\r\n        // This cannot happen in MainTemplate because the exports mismatch between\r\n        // templating and execution.\r\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\r\n        // A module can be accepted automatically based on its exports, e.g. when\r\n        // it is a Refresh Boundary.\r\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\r\n            // Save the previous exports signature on update so we can compare the boundary\r\n            // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\r\n            module.hot.dispose(function (data) {\r\n                data.prevSignature =\r\n                    self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\r\n            });\r\n            // Unconditionally accept an update to this module, we'll check if it's\r\n            // still a Refresh Boundary later.\r\n            // @ts-ignore importMeta is replaced in the loader\r\n            module.hot.accept();\r\n            // This field is set when the previous version of this module was a\r\n            // Refresh Boundary, letting us know we need to check for invalidation or\r\n            // enqueue an update.\r\n            if (prevSignature !== null) {\r\n                // A boundary can become ineligible if its exports are incompatible\r\n                // with the previous exports.\r\n                //\r\n                // For example, if you add/remove/change exports, we'll want to\r\n                // re-execute the importing modules, and force those components to\r\n                // re-render. Similarly, if you convert a class component to a\r\n                // function, we want to invalidate the boundary.\r\n                if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\r\n                    module.hot.invalidate();\r\n                }\r\n                else {\r\n                    self.$RefreshHelpers$.scheduleUpdate();\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // Since we just executed the code for the module, it's possible that the\r\n            // new exports made it ineligible for being a boundary.\r\n            // We only care about the case when we were _previously_ a boundary,\r\n            // because we already accepted this update (accidental side effect).\r\n            var isNoLongerABoundary = prevSignature !== null;\r\n            if (isNoLongerABoundary) {\r\n                module.hot.invalidate();\r\n            }\r\n        }\r\n    }\r\n})();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/common/SelectedPlanCardWithDetails/SelectedPlanCardWithDetails.tsx\n"));

/***/ })

});