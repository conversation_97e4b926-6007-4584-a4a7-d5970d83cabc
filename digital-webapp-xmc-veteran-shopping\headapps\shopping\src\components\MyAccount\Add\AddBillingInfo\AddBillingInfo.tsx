import { faChevronDown } from '@fortawesome/pro-light-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Checkbox, Select, TextInput, UnstyledButton } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import {
  Field,
  LinkField,
  RichText,
  Text,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import QuestionCircle from 'assets/icons/QuestionCircle';
import axios from 'axios-1.4';
import Tooltip from 'components/Elements/Tooltip/Tooltip';
import { useEffect, useState } from 'react';
import { GetPaperlessBillingResponse } from 'src/services/MyAccountAPI/types';
import { Translation } from 'src/types/global';
import { AddOrderInfoFormType } from '../AddOrderInfoContainer/AddOrderInfoContainer';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { setEV } from 'src/stores/planSlice';
import { useDispatch } from 'react-redux';
import { EVDisclaimer } from 'components/MiniConfirmation/EVSelection/EVSelection';
import { isTxu } from 'src/utils/util';

type AddBillingInfoProps = {
  fields: {
    BillingInformationTitle: Field<string>;
    SameAsServiceAddressCheckText: Field<string>;
    POBoxCheckText: Field<string>;
    POBoxText: Translation;
    StreetNumberText: Translation;
    StreetAddressText: Translation;
    AptOrUnitText: Translation;
    CityText: Translation;
    StateText: Translation;
    ZipcodeText: Translation;
    SecondaryAccountHolderCheckText: Field<string>;
    SecondaryAccountHolderTooltipText: Field<string>;
    SAHFirstNameText: Field<string>;
    SAHLastNameText: Field<string>;
    States: { displayName: string }[];
    PaperlessBillingCheckText: Field<string>;
    TermsAndCondition: LinkField;
    EmailForPaperlessBillingText: Field<string>;
    EVDisclaimer: EVDisclaimer;
  };
  form: UseFormReturnType<AddOrderInfoFormType>;
};

interface PaperlessStatusType {
  [key: string]: {
    paperlessStatus: boolean;
    paperlessEmail: string;
  };
}

const AddBillingInfo = (props: AddBillingInfoProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let ev = undefined;
  let dispatch: ReturnType<typeof useAppDispatch>;
  if (!isPageEditing) {
    ev = useAppSelector((state) => state.plans?.selectedPlan.ev);
    dispatch = useDispatch();
  }
  const [isSameAddress, setIsSameAddress] = useState(true);
  const [isPoBox, setIsPoBox] = useState(false);
  const isSecondaryAccountHolder = props.form.values.isSecondaryAccountHolder;
  const isPaperlessBilling = props.form.values.isPaperlessBilling;
  const [paperlessStatus, setPaperlessStatus] = useState<PaperlessStatusType>({});
  const isEV = ev && props.fields.EVDisclaimer?.fields?.EVModels?.value?.includes(ev);
  const [isEVDisclaimerSelected, setIsEVDisclaimerSelected] = useState(false);
  const [custMaillingAddress, setCustMaillingAddress] = useState('');

  useEffect(() => {
    if (!isPageEditing) {
      dispatch(
        setEV({
          isEVSelected: isEVDisclaimerSelected,
          EVDisclaimerMessage: props.fields.EVDisclaimer?.fields?.EVDisclaimerMessage?.value,
          DiclaimerErrorText: '',
          EVModels: props.fields.EVDisclaimer?.fields?.EVDisclaimerMessage?.value,
        })
      );
    }
  }, [
    dispatch,
    isPageEditing,
    isEVDisclaimerSelected,
    props.fields.EVDisclaimer?.fields?.EVDisclaimerMessage?.value,
  ]);

  useEffect(() => {
    async function getPaperlessStatus() {
      const paperlessReq = await axios.get<GetPaperlessBillingResponse>(
        '/api/myaccount/paperlessbilling',
        {
          params: {
            ca: contractAccount,
          },
        }
      );

      if (paperlessReq.data) {
        props.form.setFieldValue('isPaperlessBilling', paperlessReq.data.result.isPaperLess);
        if (paperlessReq.data.result.isPaperLess) {
          props.form.setFieldValue('paperlessBillingEmail', paperlessReq.data.result.userEmail);
        } else {
          props.form.setFieldValue('paperlessBillingEmail', '');
        }
        setPaperlessStatus({
          ...paperlessStatus,
          [contractAccount]: {
            paperlessStatus: paperlessReq.data.result.isPaperLess,
            paperlessEmail: paperlessReq.data.result.userEmail,
          },
        });
      }
    }

    const contractAccount = props.form.values.contractAccount;

    if (contractAccount) {
      if (paperlessStatus[contractAccount] === undefined) {
        getPaperlessStatus();
      } else if (typeof paperlessStatus[contractAccount].paperlessStatus === 'boolean') {
        props.form.setFieldValue(
          'isPaperlessBilling',
          paperlessStatus[contractAccount].paperlessStatus
        );
        if (paperlessStatus[contractAccount].paperlessStatus) {
          props.form.setFieldValue(
            'paperlessBillingEmail',
            paperlessStatus[contractAccount].paperlessEmail
          );
        } else {
          props.form.setFieldValue('paperlessBillingEmail', '');
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.form.values.contractAccount]);

  useEffect(() => {
    const getCustomerDetails = async () => {
      const isExistingBill = props.form.values.isExistingBill;
      const contractAccountNumber = props.form.values.contractAccount;
      let formatCustMaillingAdd = '';
      if (isExistingBill === 'true' && contractAccountNumber != '') {
        console.log(isExistingBill);
        console.log(props.form.values.contractAccount);
        const req = await axios.get('/api/myaccount/customerdetails', {
          params: {
            ca: contractAccountNumber,
          },
        });

        console.log(req.data);
        formatCustMaillingAdd =
          req.data?.result?.mailingAddress?.streetNumber +
          ' ' +
          req.data?.result?.mailingAddress?.streetName +
          ' ' +
          req.data?.result?.mailingAddress?.aptNumber +
          ' ' +
          req.data?.result?.mailingAddress?.city +
          ' ' +
          req.data?.result?.mailingAddress?.state +
          ' ' +
          req.data?.result?.mailingAddress?.postalCode;
        console.log(formatCustMaillingAdd);
        setCustMaillingAddress(formatCustMaillingAdd);
      }
    };
    getCustomerDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.form.values.isExistingBill && props.form.values.contractAccount]);

  function handleSameAddressChange(event: React.ChangeEvent<HTMLInputElement>) {
    const isSameAddressChecked = event.currentTarget.checked;
    setIsSameAddress(isSameAddressChecked);
    if (isSameAddressChecked) {
      props.form.setFieldValue('billingOption', 'sameAddress');
    } else {
      props.form.setFieldValue('billingOption', 'differentAddress');
    }
  }

  function handlePoBoxChange(event: React.ChangeEvent<HTMLInputElement>) {
    const isPoBoxChecked = event.currentTarget.checked;
    setIsPoBox(isPoBoxChecked);
    if (isPoBoxChecked) {
      props.form.setFieldValue('billingOption', 'poBox');
    } else {
      props.form.setFieldValue('billingOption', 'differentAddress');
    }
  }

  return (
    <div>
      <Text
        tag="p"
        className="font-primaryBlack text-base sm:text-plus2 text-textQuattuordenary"
        field={{ value: props.fields.BillingInformationTitle.value }}
      />
      <div className="flex flex-col gap-6 mt-8">
        {props.form.values.isExistingBill === 'false' ? (
          <div>
            <div className="flex gap-8 flex-col">
              <Checkbox
                radius={0}
                size="xs"
                label={props.fields.SameAsServiceAddressCheckText.value}
                checked={isSameAddress}
                onChange={handleSameAddressChange}
                styles={() => {
                  if (!isTxu)
                    return {
                      label: {
                        fontFamily: 'GothaPro-Bold',
                      },
                    };
                  else return {};
                }}
              />
              {!isSameAddress ? (
                <Checkbox
                  radius={0}
                  size="xs"
                  checked={isPoBox}
                  onChange={handlePoBoxChange}
                  label={props.fields.POBoxCheckText.value}
                  styles={() => {
                    if (!isTxu)
                      return {
                        label: {
                          fontFamily: 'GothaPro-Bold',
                        },
                      };
                    else return {};
                  }}
                />
              ) : (
                <></>
              )}
            </div>
            {!isSameAddress && !isPoBox && (
              <div className="flex flex-col gap-5 sm:gap-8 mt-2">
                <div className="flex flex-col sm:flex-row gap-5 sm:gap-6">
                  <TextInput
                    label={props.fields.StreetNumberText.fields.Message.value}
                    {...props.form.getInputProps('billingStreetNumber')}
                  />
                  <TextInput
                    label={props.fields.StreetAddressText.fields.Message.value}
                    {...props.form.getInputProps('billingStreetAddress')}
                  />
                  <TextInput
                    label={props.fields.AptOrUnitText.fields.Message.value}
                    {...props.form.getInputProps('billingAptOrUnit')}
                  />
                </div>
                <div className="flex flex-col gap-5 sm:flex-row sm:gap-6">
                  <TextInput
                    label={props.fields.CityText.fields.Message.value}
                    {...props.form.getInputProps('billingCity')}
                  />
                  <Select
                    styles={{
                      wrapper: {
                        [`@media (max-width: 767px)`]: {
                          width: '100%',
                        },
                      },
                      root: {
                        [`@media (max-width: 767px)`]: {
                          width: '50%',
                        },
                      },
                    }}
                    data={props.fields.States.map((state) => state.displayName)}
                    label="State"
                    {...props.form.getInputProps('billingState')}
                    rightSection={
                      <FontAwesomeIcon
                        icon={faChevronDown}
                        className="text-textSecondary hover:text-textPrimary"
                      />
                    }
                    selectOnBlur
                  />
                  <TextInput
                    label={props.fields.ZipcodeText.fields.Message.value}
                    {...props.form.getInputProps('billingZipCode')}
                    styles={{
                      root: {
                        [`@media (max-width: 767px)`]: {
                          width: '50%',
                        },
                      },
                    }}
                  />
                </div>
              </div>
            )}
            {!isSameAddress && isPoBox && (
              <div className="flex flex-col gap-8">
                <div className="flex flex-row gap-6">
                  <TextInput
                    label={props.fields.POBoxText.fields.Message.value}
                    {...props.form.getInputProps('poBox')}
                  />
                  <TextInput
                    label={props.fields.CityText.fields.Message.value}
                    {...props.form.getInputProps('poBoxCity')}
                  />
                </div>
                <div className="flex flex-row gap-6">
                  <Select
                    styles={{
                      wrapper: {
                        width: '156px',
                      },
                    }}
                    data={props.fields.States.map((state) => state.displayName)}
                    label="State"
                    {...props.form.getInputProps('poBoxState')}
                    rightSection={
                      <FontAwesomeIcon
                        icon={faChevronDown}
                        className="text-textSecondary hover:text-textPrimary"
                      />
                    }
                    selectOnBlur
                  />
                  <TextInput
                    label={props.fields.ZipcodeText.fields.Message.value}
                    {...props.form.getInputProps('poBoxZipCode')}
                  />
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-sm">
            <span className="font-primaryBold ">Mailling Address: </span>
            {custMaillingAddress}
          </div>
        )}
        <div className="flex flex-row gap-1">
          <Checkbox
            styles={{
              labelWrapper: {
                ['@media (max-width: 640px)']: {
                  width: '270px',
                },
              },
            }}
            checked={isSecondaryAccountHolder}
            {...props.form.getInputProps('isSecondaryAccountHolder')}
            label={props.fields.SecondaryAccountHolderCheckText.value}
            radius="xs"
            size="xs"
          />
          <Tooltip
            content={{
              value: props.fields.SecondaryAccountHolderTooltipText.value,
            }}
            className="billing-tooltip"
            arrowclassName="billing-tooltip-icon"
          >
            <QuestionCircle />
          </Tooltip>
        </div>
        {isSecondaryAccountHolder && (
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
            <TextInput
              label={props.fields.SAHFirstNameText.value}
              {...props.form.getInputProps('secondaryAccountFirstName')}
            />
            <TextInput
              label={props.fields.SAHLastNameText.value}
              {...props.form.getInputProps('secondaryAccountLastName')}
            />
          </div>
        )}
        {/* <Checkbox
          {...props.form.getInputProps('isPaperlessBilling')}
          checked={isPaperlessBilling}
          radius={0}
          size="xs"
          label={
            <div>
              <RichText
                tag="p"
                className="inline-link font-primaryRegular leading-[25px] mb-[5px]"
                field={{
                  value: props.fields.PaperlessBillingCheckText.value,
                }}
              /> */}

              {/* <Link href="/">
                <a
                  className="text-digitalBlueBonnet underline decoration-solid decoration-digitalBlueBonnet decoration-2"
                  href={props.fields.TermsAndCondition.value.href}
                >
                  {props.fields.TermsAndCondition.value.text}
                </a>
              </Link> */}
              {/* <UnstyledButton
                component="a"
                className="text-textSecondary hover:text-textPrimary underline decoration-solid  decoration-2 decoration-textPrimary decoration-offset-4 font-primaryRegular"
                href={props.fields.TermsAndCondition.value.href}
                target="_blank"
              >
                {props.fields.TermsAndCondition.value.text}
              </UnstyledButton>
            </div>
          }
        /> */}
        {/* {isPaperlessBilling ? (
          <TextInput
            styles={{
              root: {
                width: '280px',
                ['@media (max-width: 640px)']: {
                  width: '100%',
                },
              },
            }}
            {...props.form.getInputProps('paperlessBillingEmail')}
            label={props.fields.EmailForPaperlessBillingText.value}
          />
        ) : (
          <></>
        )} */}
        {isEV && (
          <Checkbox
            radius={0}
            size="xs"
            checked={isEVDisclaimerSelected}
            onChange={() => setIsEVDisclaimerSelected(!isEVDisclaimerSelected)}
            label={props.fields.EVDisclaimer.fields.EVCheckbox.value}
          />
        )}
      </div>
    </div>
  );
};

export { AddBillingInfo };
// const Component = withDatasourceCheck()<AddBillingInfoProps>(AddBillingInfo);
// export default aiLogger(Component, Component.name);
export default AddBillingInfo;
